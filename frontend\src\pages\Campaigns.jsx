import React from 'react';
import Sidebar from '../partials/Sidebar';
import Header from '../partials/Header';

function Campaigns() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  return (
    <div className="flex h-[100dvh] overflow-hidden">
      {/* Sidebar */}
      <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {/* Site header */}
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        <main className="grow">
          <div className="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
            {/* Page header */}
            <div className="mb-8">
              <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">Campaigns</h1>
            </div>

            {/* Content */}
            <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Marketing Campaigns</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Create and manage your marketing campaigns.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white">
                  <h3 className="text-lg font-semibold mb-2">Summer Sale</h3>
                  <p className="text-blue-100 mb-4">Active campaign running until end of month</p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">75% Complete</span>
                    <span className="bg-white/20 px-2 py-1 rounded text-xs">Active</span>
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white">
                  <h3 className="text-lg font-semibold mb-2">Product Launch</h3>
                  <p className="text-green-100 mb-4">New product announcement campaign</p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">45% Complete</span>
                    <span className="bg-white/20 px-2 py-1 rounded text-xs">Planning</span>
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white">
                  <h3 className="text-lg font-semibold mb-2">Holiday Special</h3>
                  <p className="text-purple-100 mb-4">Seasonal promotion campaign</p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">20% Complete</span>
                    <span className="bg-white/20 px-2 py-1 rounded text-xs">Draft</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Campaigns;
