# 🔧 Guide de Résolution des Problèmes - Modèles 3D

## 🚨 **Problèmes Courants et Solutions**

### **1. Le modèle 3D n'est pas visible ou apparaît comme un point**

#### **Symptômes :**
- Écran gris avec juste un petit point sombre
- Modèle généré mais pas visible dans le viewer
- Message "736 vertices" mais rien à l'écran

#### **Causes possibles :**
- Problème d'échelle du modèle
- Vertices mal positionnés
- Caméra mal positionnée
- Erreur dans la génération des faces

#### **Solutions :**

**A. Vérifier les données du modèle :**
```javascript
// Dans la console du navigateur
console.log("Vertices:", modelData.vertices);
console.log("Faces:", modelData.faces);
console.log("Bounding box:", geometry.boundingBox);
```

**B. Ajuster l'échelle et la position :**
```javascript
// Dans Model3DViewer.jsx
geometry.computeBoundingBox();
const center = new THREE.Vector3();
geometry.boundingBox.getCenter(center);
geometry.translate(-center.x, -center.y, -center.z);

const size = new THREE.Vector3();
geometry.boundingBox.getSize(size);
const maxDimension = Math.max(size.x, size.y, size.z);
const scale = 2 / maxDimension; // Ajuster cette valeur
geometry.scale(scale, scale, scale);
```

**C. Vérifier la position de la caméra :**
```javascript
camera.position.set(0, 0, 3); // Reculer la caméra
camera.lookAt(0, 0, 0);
```

### **2. Le modèle apparaît déformé ou non-humanoïde**

#### **Symptômes :**
- Forme bizarre, non-humaine
- Proportions incorrectes
- Modèle trop étiré ou compressé

#### **Solutions :**

**A. Améliorer l'algorithme de génération :**
```python
# Dans backend/generateur/views.py
def create_humanoid_model(measurements, gender):
    # Vérifier les proportions
    height = max(measurements.get('height', 170), 150) / 100.0
    chest_width = max(measurements.get('chest', 90), 70) / 100.0
    
    # Limiter les valeurs extrêmes
    height = min(height, 2.2)  # Max 2.2m
    chest_width = min(chest_width, 1.5)  # Max 1.5m
```

**B. Ajuster les segments du corps :**
```python
segments = [
    # [y_start, y_end, width_start, width_end, depth_start, depth_end]
    [0.0, 0.15, hip_width*0.25, hip_width*0.3, waist_width*0.15, waist_width*0.2],  # Pieds
    [0.15, 0.5, hip_width*0.35, hip_width*0.7, waist_width*0.25, waist_width*0.4],  # Jambes
    [0.5, 0.65, hip_width*0.85, hip_width, waist_width*0.4, waist_width*0.5],       # Hanches
    [0.65, 0.8, waist_width, chest_width*0.9, waist_width*0.4, chest_width*0.45],  # Taille
    [0.8, 0.95, chest_width, shoulder_width, chest_width*0.45, shoulder_width*0.3], # Torse
    [0.95, 1.0, shoulder_width*0.5, shoulder_width*0.3, shoulder_width*0.15, shoulder_width*0.1] # Cou
]
```

### **3. Erreurs de rendu Three.js**

#### **Symptômes :**
- Erreurs dans la console du navigateur
- Modèle qui clignote ou disparaît
- Performance dégradée

#### **Solutions :**

**A. Vérifier les indices des faces :**
```javascript
// S'assurer que tous les indices sont valides
const maxIndex = Math.max(...modelData.faces);
const vertexCount = modelData.vertices.length / 3;
if (maxIndex >= vertexCount) {
    console.error("Index de face invalide:", maxIndex, ">=", vertexCount);
}
```

**B. Recalculer les normales :**
```javascript
geometry.computeVertexNormals();
geometry.normalizeNormals();
```

**C. Vérifier la validité de la géométrie :**
```javascript
if (geometry.attributes.position.count === 0) {
    console.error("Géométrie vide");
    return;
}
```

### **4. Problèmes de couleur et matériaux**

#### **Symptômes :**
- Modèle trop sombre ou trop clair
- Couleur incorrecte
- Pas d'ombres

#### **Solutions :**

**A. Ajuster l'éclairage :**
```javascript
// Éclairage ambiant plus fort
const ambientLight = new THREE.AmbientLight(0x404040, 0.8); // Augmenter l'intensité

// Éclairage directionnel multiple
const light1 = new THREE.DirectionalLight(0xffffff, 0.6);
light1.position.set(1, 1, 1);

const light2 = new THREE.DirectionalLight(0xffffff, 0.4);
light2.position.set(-1, -1, -1);
```

**B. Améliorer le matériau :**
```javascript
const material = new THREE.MeshPhongMaterial({
    color: skinColor,
    shininess: 30,
    specular: 0x111111,
    side: THREE.DoubleSide
});
```

### **5. Problèmes de performance**

#### **Symptômes :**
- Lag lors de la rotation
- Framerate faible
- Navigateur qui rame

#### **Solutions :**

**A. Optimiser la géométrie :**
```python
# Réduire le nombre de segments
segments_per_level = 12  # Au lieu de 16
levels = 4  # Au lieu de 5
```

**B. Utiliser des LOD (Level of Detail) :**
```javascript
const lod = new THREE.LOD();
lod.addLevel(highDetailMesh, 0);
lod.addLevel(lowDetailMesh, 50);
scene.add(lod);
```

## 🧪 **Tests de Diagnostic**

### **Test 1: Vérification des données**
```javascript
function testModelData(modelData) {
    console.log("=== DIAGNOSTIC MODÈLE 3D ===");
    console.log("Type:", modelData.model_type);
    console.log("Vertices count:", modelData.vertices_count);
    console.log("Faces count:", modelData.faces_count);
    console.log("Vertices length:", modelData.vertices?.length);
    console.log("Faces length:", modelData.faces?.length);
    
    if (modelData.vertices) {
        const minX = Math.min(...modelData.vertices.filter((_, i) => i % 3 === 0));
        const maxX = Math.max(...modelData.vertices.filter((_, i) => i % 3 === 0));
        const minY = Math.min(...modelData.vertices.filter((_, i) => i % 3 === 1));
        const maxY = Math.max(...modelData.vertices.filter((_, i) => i % 3 === 1));
        const minZ = Math.min(...modelData.vertices.filter((_, i) => i % 3 === 2));
        const maxZ = Math.max(...modelData.vertices.filter((_, i) => i % 3 === 2));
        
        console.log("Bounding box:");
        console.log("  X:", minX, "to", maxX);
        console.log("  Y:", minY, "to", maxY);
        console.log("  Z:", minZ, "to", maxZ);
    }
}
```

### **Test 2: Validation de la géométrie**
```javascript
function validateGeometry(geometry) {
    console.log("=== VALIDATION GÉOMÉTRIE ===");
    console.log("Position attribute:", geometry.attributes.position);
    console.log("Index:", geometry.index);
    console.log("Bounding box:", geometry.boundingBox);
    
    if (geometry.index) {
        const maxIndex = Math.max(...geometry.index.array);
        const vertexCount = geometry.attributes.position.count;
        console.log("Max index:", maxIndex, "Vertex count:", vertexCount);
        
        if (maxIndex >= vertexCount) {
            console.error("❌ Index invalide détecté!");
            return false;
        }
    }
    
    console.log("✅ Géométrie valide");
    return true;
}
```

### **Test 3: Performance**
```javascript
function measurePerformance() {
    const start = performance.now();
    
    // Votre code de rendu ici
    
    const end = performance.now();
    console.log("Temps de rendu:", (end - start).toFixed(2), "ms");
}
```

## 🔧 **Outils de Debug**

### **1. Mode Debug dans le Viewer**
```javascript
// Ajouter dans Model3DViewer.jsx
const [debugMode, setDebugMode] = useState(false);

// Afficher les axes de coordonnées
if (debugMode) {
    const axesHelper = new THREE.AxesHelper(1);
    scene.add(axesHelper);
    
    // Afficher la bounding box
    const box = new THREE.BoxHelper(mesh, 0xffff00);
    scene.add(box);
}
```

### **2. Console de Debug**
```javascript
// Ajouter des logs détaillés
console.group("🔍 Debug Modèle 3D");
console.log("Données reçues:", modelData);
console.log("Géométrie créée:", geometry);
console.log("Matériau:", material);
console.log("Mesh final:", mesh);
console.groupEnd();
```

### **3. Wireframe automatique pour debug**
```javascript
// Forcer le wireframe en cas de problème
if (modelData.vertices_count < 100) {
    material.wireframe = true;
    console.warn("⚠️ Peu de vertices détectés, passage en wireframe");
}
```

## 📋 **Checklist de Résolution**

### **Avant de signaler un bug :**
- [ ] Vérifier la console du navigateur pour les erreurs
- [ ] Tester avec différentes mesures corporelles
- [ ] Vérifier que les serveurs backend et frontend sont démarrés
- [ ] Tester le mode wireframe
- [ ] Vérifier les données retournées par l'API
- [ ] Tester sur un autre navigateur
- [ ] Vérifier la version de Three.js

### **Informations à fournir :**
- [ ] Version du navigateur
- [ ] Messages d'erreur de la console
- [ ] Données de test utilisées
- [ ] Screenshots du problème
- [ ] Logs du serveur Django

## 🚀 **Améliorations Futures**

### **Optimisations prévues :**
- [ ] Génération de modèles plus détaillés
- [ ] Algorithme de lissage des surfaces
- [ ] Support des textures
- [ ] Animation des modèles
- [ ] Compression des données de vertices

### **Fonctionnalités de debug :**
- [ ] Interface de debug intégrée
- [ ] Visualisation des normales
- [ ] Statistiques de performance en temps réel
- [ ] Export des logs de debug

---

## 📞 **Support**

Si le problème persiste après avoir suivi ce guide :

1. **Vérifiez les logs** du serveur Django
2. **Testez l'API** directement avec `python test_3d_api.py`
3. **Consultez la documentation** Three.js pour les erreurs WebGL
4. **Redémarrez** les serveurs backend et frontend

**Le modèle humanoïde amélioré devrait maintenant afficher un corps humain reconnaissable avec 736 vertices et 1,104 faces !** 🎉
