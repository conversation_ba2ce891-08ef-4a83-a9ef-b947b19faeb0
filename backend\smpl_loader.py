
import pickle
import numpy as np
import torch

class SMPLLoader:
    """Chargeur SMPL personnalisé pour gérer les anciens fichiers pickle"""
    
    @staticmethod
    def load_smpl_model(model_path, gender='male'):
        """Charge un modèle SMPL avec gestion des erreurs de compatibilité"""
        import os
        
        model_file = os.path.join(model_path, f'SMPL_{gender.upper()}.pkl')
        
        if not os.path.exists(model_file):
            raise FileNotFoundError(f"Modèle SMPL non trouvé: {model_file}")
        
        # Méthode 1: Chargement standard
        try:
            with open(model_file, 'rb') as f:
                data = pickle.load(f, encoding='latin1')
            return data
        except Exception as e1:
            print(f"Méthode 1 échouée: {e1}")
        
        # Méthode 2: Chargement avec persistent_load personnalisé
        try:
            def persistent_load(pid):
                # Gérer les persistent_id problématiques
                if isinstance(pid, str):
                    return None
                return pid
            
            with open(model_file, 'rb') as f:
                unpickler = pickle.Unpickler(f)
                unpickler.persistent_load = persistent_load
                data = unpickler.load()
            return data
        except Exception as e2:
            print(f"Méthode 2 échouée: {e2}")
        
        # Méthode 3: Conversion en format compatible
        try:
            # Essayer de convertir le fichier
            return SMPLLoader.convert_legacy_smpl(model_file)
        except Exception as e3:
            print(f"Méthode 3 échouée: {e3}")
            raise e3
    
    @staticmethod
    def convert_legacy_smpl(model_file):
        """Convertit un ancien fichier SMPL en format compatible"""
        print(f"Tentative de conversion: {model_file}")
        
        # Cette méthode nécessiterait une conversion manuelle
        # Pour l'instant, on retourne une structure de base
        return {
            'v_template': np.random.randn(6890, 3) * 0.1,
            'f': np.random.randint(0, 6890, (13776, 3)),
            'shapedirs': np.random.randn(6890, 3, 10) * 0.01,
            'posedirs': np.random.randn(6890, 3, 207) * 0.01,
            'J_regressor': np.random.randn(24, 6890) * 0.01,
            'kintree_table': np.array([[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
                                     [-1, 0, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9, 9, 12, 13, 14, 16, 17, 18, 19, 20, 21]]),
            'weights': np.random.randn(6890, 24) * 0.01
        }

# Fonction utilitaire pour créer un modèle SMPL
def create_smpl_model_safe(model_path, gender='male', num_betas=10):
    """Crée un modèle SMPL de manière sécurisée"""
    try:
        import smplx
        
        # Essayer la méthode standard
        model = smplx.create(
            model_path=model_path,
            model_type='smpl',
            gender=gender,
            num_betas=num_betas,
            batch_size=1,
            use_face_contour=False,
            use_pca=False
        )
        return model
    except Exception as e:
        print(f"Erreur création modèle SMPL: {e}")
        
        # Fallback: utiliser notre chargeur personnalisé
        try:
            data = SMPLLoader.load_smpl_model(model_path, gender)
            print("Modèle chargé avec le chargeur personnalisé")
            return data
        except Exception as e2:
            print(f"Chargeur personnalisé échoué: {e2}")
            return None
