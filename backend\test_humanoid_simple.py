#!/usr/bin/env python3
"""
Test simple du modèle humanoïde avancé
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mannequin_project.settings')
django.setup()

def test_humanoid_generation():
    """Test de génération du modèle humanoïde"""
    print("🧪 Test Modèle Humanoïde Avancé")
    print("=" * 50)
    
    try:
        from generateur.views import create_humanoid_model, analyze_body_morphology, measurements_to_smpl_betas
        
        # Mesures de test
        measurements = {
            'height': 175.0,
            'weight': 70.0,
            'chest': 95.0,
            'waist': 80.0,
            'hips': 90.0,
            'shoulder_width': 45.0,
            'arm_length': 65.0,
            'inseam': 82.0
        }
        
        print(f"📏 Mesures de test: {measurements}")
        
        # Test de l'analyse morphologique
        analysis = analyze_body_morphology(measurements)
        print(f"🧬 Analyse morphologique:")
        print(f"   - IMC: {analysis['bmi']} ({analysis['bmi_category']})")
        print(f"   - Type de corps: {analysis['body_type']}")
        print(f"   - Catégorie taille: {analysis['height_category']}")
        print(f"   - Ratio épaules/hanches: {analysis['shoulder_hip_ratio']}")
        
        # Test de conversion en betas
        betas = measurements_to_smpl_betas(measurements)
        print(f"🧬 Betas SMPL: {betas}")
        
        # Test de génération du modèle
        vertices, faces = create_humanoid_model(measurements, 'male')
        
        print(f"✅ Modèle généré avec succès!")
        print(f"   - Vertices: {len(vertices):,}")
        print(f"   - Faces: {len(faces):,}")
        
        # Analyse des vertices
        if vertices:
            import numpy as np
            vertices_array = np.array(vertices)
            
            print(f"📊 Statistiques du modèle:")
            print(f"   - Bounding box X: [{vertices_array[:, 0].min():.3f}, {vertices_array[:, 0].max():.3f}]")
            print(f"   - Bounding box Y: [{vertices_array[:, 1].min():.3f}, {vertices_array[:, 1].max():.3f}]")
            print(f"   - Bounding box Z: [{vertices_array[:, 2].min():.3f}, {vertices_array[:, 2].max():.3f}]")
            
            # Vérifier que le modèle a une forme humanoïde
            height_model = vertices_array[:, 1].max() - vertices_array[:, 1].min()
            width_model = vertices_array[:, 0].max() - vertices_array[:, 0].min()
            depth_model = vertices_array[:, 2].max() - vertices_array[:, 2].min()
            
            print(f"   - Dimensions: H={height_model:.3f}, L={width_model:.3f}, P={depth_model:.3f}")
            print(f"   - Ratio H/L: {height_model/width_model:.2f} (doit être > 2 pour un humain)")
            
            if height_model / width_model > 2:
                print("✅ Proportions humanoïdes correctes!")
            else:
                print("⚠️ Proportions à ajuster")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_genders():
    """Test avec différents genres"""
    print("\n👫 Test Différents Genres")
    print("=" * 50)
    
    try:
        from generateur.views import create_humanoid_model
        
        measurements = {
            'height': 165.0,
            'weight': 60.0,
            'chest': 85.0,
            'waist': 70.0,
            'hips': 95.0,
            'shoulder_width': 40.0
        }
        
        for gender in ['male', 'female', 'neutral']:
            print(f"\n🧑 Test genre: {gender}")
            vertices, faces = create_humanoid_model(measurements, gender)
            print(f"   - Vertices: {len(vertices):,}")
            print(f"   - Faces: {len(faces):,}")
            
            if vertices:
                import numpy as np
                vertices_array = np.array(vertices)
                width = vertices_array[:, 0].max() - vertices_array[:, 0].min()
                print(f"   - Largeur: {width:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_extreme_measurements():
    """Test avec des mesures extrêmes"""
    print("\n🔬 Test Mesures Extrêmes")
    print("=" * 50)
    
    test_cases = [
        {
            'name': 'Personne très grande',
            'measurements': {'height': 200, 'weight': 90, 'chest': 110, 'waist': 90, 'hips': 100, 'shoulder_width': 55}
        },
        {
            'name': 'Personne petite',
            'measurements': {'height': 150, 'weight': 50, 'chest': 75, 'waist': 65, 'hips': 80, 'shoulder_width': 35}
        },
        {
            'name': 'Morphologie athlétique',
            'measurements': {'height': 180, 'weight': 80, 'chest': 105, 'waist': 75, 'hips': 90, 'shoulder_width': 50}
        }
    ]
    
    try:
        from generateur.views import create_humanoid_model, analyze_body_morphology
        
        for case in test_cases:
            print(f"\n📋 {case['name']}:")
            measurements = case['measurements']
            
            analysis = analyze_body_morphology(measurements)
            vertices, faces = create_humanoid_model(measurements, 'male')
            
            print(f"   - IMC: {analysis['bmi']} ({analysis['bmi_category']})")
            print(f"   - Type: {analysis['body_type']}")
            print(f"   - Modèle: {len(vertices)} vertices, {len(faces)} faces")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Tests Complets Modèle Humanoïde")
    print("=" * 60)
    
    success_count = 0
    
    if test_humanoid_generation():
        success_count += 1
    
    if test_different_genders():
        success_count += 1
    
    if test_extreme_measurements():
        success_count += 1
    
    print(f"\n📊 Résultats: {success_count}/3 tests réussis")
    
    if success_count == 3:
        print("🎉 Modèle humanoïde avancé parfaitement fonctionnel!")
        print("💡 Prêt pour l'utilisation en production")
        print("🔗 Testez maintenant sur: http://localhost:5174/3d-body")
    else:
        print("⚠️ Certains tests ont échoué")
        print("💡 Vérifiez les logs pour plus de détails")
