import React, { useState } from "react";
import Sidebar from "../../partials/Sidebar";
import Header from "../../partials/Header";

function Account() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [measurements, setMeasurements] = useState({
    // Personal Information
    firstName: "",
    lastName: "",
    gender: "male",
    age: "",

    // Basic Measurements
    height: "",
    weight: "",

    // Body Measurements (in cm)
    chest: "",
    waist: "",
    hips: "",
    shoulderWidth: "",
    neckCircumference: "",

    // Arm Measurements
    armLength: "",
    armSpan: "",
    wristCircumference: "",

    // Leg Measurements
    inseam: "",
    thighCircumference: "",
    calfCircumference: "",
    ankleCircumference: "",

    // Torso Measurements
    torsoLength: "",
    backWidth: "",

    // Head and Foot
    headCircumference: "",
    footLength: "",
    footWidth: "",
  });

  const [unit, setUnit] = useState("cm"); // cm or inches
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState("");

  const handleInputChange = (field, value) => {
    setMeasurements((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage("");

    try {
      // Convert measurements to cm if needed
      const measurementsInCm = { ...measurements };
      if (unit === "inches") {
        const fieldsToConvert = [
          "height",
          "chest",
          "waist",
          "hips",
          "shoulderWidth",
          "neckCircumference",
          "armLength",
          "armSpan",
          "wristCircumference",
          "inseam",
          "thighCircumference",
          "calfCircumference",
          "ankleCircumference",
          "torsoLength",
          "backWidth",
          "headCircumference",
          "footLength",
          "footWidth",
        ];

        fieldsToConvert.forEach((field) => {
          if (measurementsInCm[field]) {
            measurementsInCm[field] = (
              parseFloat(measurementsInCm[field]) * 2.54
            ).toString();
          }
        });
      }

      // Send to backend for 3D model generation
      const response = await fetch("http://localhost:8000/3d-body/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          genre: measurements.gender,
          taille: parseFloat(measurementsInCm.height) || 170,
          weight: parseFloat(measurementsInCm.weight) || 70,
          poitrine: parseFloat(measurementsInCm.chest) || 90,
          tour_taille: parseFloat(measurementsInCm.waist) || 75,
          hanches: parseFloat(measurementsInCm.hips) || 95,
          additional_measurements: measurementsInCm,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Créer un message détaillé avec les informations du modèle
        let message = `✅ ${result.message}\n`;
        message += `📊 Modèle: ${result.model_type}\n`;
        message += `🔢 Vertices: ${result.vertices_count.toLocaleString()}\n`;
        message += `🔺 Faces: ${result.faces_count.toLocaleString()}\n`;

        if (result.measurement_analysis) {
          message += `📊 Analyse:\n`;
          message += `  - Taille: ${result.measurement_analysis.height_category}\n`;
          message += `  - Morphologie: ${result.measurement_analysis.body_type}\n`;
          message += `  - IMC: ${result.measurement_analysis.bmi}\n`;
        }

        if (result.model_info && !result.model_info.has_real_model) {
          message += `\n⚠️ ${result.model_info.note}`;
        }

        setSubmitMessage(message);

        // Log des informations détaillées pour le développement
        console.log("Modèle 3D généré:", result);
        console.log("Paramètres SMPL betas:", result.betas);
      } else {
        setSubmitMessage(
          "❌ Erreur lors de la génération: " +
            (result.error || "Erreur inconnue")
        );
      }
    } catch (error) {
      setSubmitMessage("Erreur de connexion: " + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const inputClassName =
    "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-violet-500 focus:border-violet-500 dark:bg-gray-700 dark:text-gray-100";
  const labelClassName =
    "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2";

  return (
    <div className="flex h-[100dvh] overflow-hidden">
      {/* Sidebar */}
      <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {/* Site header */}
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        <main className="grow">
          <div className="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
            {/* Page header */}
            <div className="mb-8">
              <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
                Mesures pour Modélisation 3D
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Saisissez vos mesures corporelles pour générer un modèle 3D
                personnalisé
              </p>
            </div>

            <form onSubmit={handleSubmit}>
              {/* Personal Information */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6">
                  Informations Personnelles
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label className={labelClassName}>Prénom</label>
                    <input
                      type="text"
                      className={inputClassName}
                      value={measurements.firstName}
                      onChange={(e) =>
                        handleInputChange("firstName", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Nom</label>
                    <input
                      type="text"
                      className={inputClassName}
                      value={measurements.lastName}
                      onChange={(e) =>
                        handleInputChange("lastName", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Genre</label>
                    <select
                      className={inputClassName}
                      value={measurements.gender}
                      onChange={(e) =>
                        handleInputChange("gender", e.target.value)
                      }
                      required
                    >
                      <option value="male">Homme</option>
                      <option value="female">Femme</option>
                      <option value="neutral">Neutre</option>
                    </select>
                  </div>
                  <div>
                    <label className={labelClassName}>Âge</label>
                    <input
                      type="number"
                      className={inputClassName}
                      value={measurements.age}
                      onChange={(e) => handleInputChange("age", e.target.value)}
                      min="1"
                      max="120"
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Unité de mesure</label>
                    <select
                      className={inputClassName}
                      value={unit}
                      onChange={(e) => setUnit(e.target.value)}
                    >
                      <option value="cm">Centimètres (cm)</option>
                      <option value="inches">Pouces (inches)</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Basic Measurements */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6">
                  Mesures de Base
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={labelClassName}>Taille ({unit})</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.height}
                      onChange={(e) =>
                        handleInputChange("height", e.target.value)
                      }
                      placeholder={unit === "cm" ? "170" : "67"}
                      required
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Poids (kg)</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.weight}
                      onChange={(e) =>
                        handleInputChange("weight", e.target.value)
                      }
                      placeholder="70"
                    />
                  </div>
                </div>
              </div>

              {/* Body Measurements */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6">
                  Mesures Corporelles ({unit})
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label className={labelClassName}>Tour de poitrine</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.chest}
                      onChange={(e) =>
                        handleInputChange("chest", e.target.value)
                      }
                      placeholder={unit === "cm" ? "90" : "35"}
                      required
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Tour de taille</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.waist}
                      onChange={(e) =>
                        handleInputChange("waist", e.target.value)
                      }
                      placeholder={unit === "cm" ? "75" : "30"}
                      required
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Tour de hanches</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.hips}
                      onChange={(e) =>
                        handleInputChange("hips", e.target.value)
                      }
                      placeholder={unit === "cm" ? "95" : "37"}
                      required
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Largeur d'épaules</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.shoulderWidth}
                      onChange={(e) =>
                        handleInputChange("shoulderWidth", e.target.value)
                      }
                      placeholder={unit === "cm" ? "45" : "18"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Tour de cou</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.neckCircumference}
                      onChange={(e) =>
                        handleInputChange("neckCircumference", e.target.value)
                      }
                      placeholder={unit === "cm" ? "38" : "15"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Longueur du torse</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.torsoLength}
                      onChange={(e) =>
                        handleInputChange("torsoLength", e.target.value)
                      }
                      placeholder={unit === "cm" ? "60" : "24"}
                    />
                  </div>
                </div>
              </div>

              {/* Arm Measurements */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6">
                  Mesures des Bras ({unit})
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label className={labelClassName}>Longueur du bras</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.armLength}
                      onChange={(e) =>
                        handleInputChange("armLength", e.target.value)
                      }
                      placeholder={unit === "cm" ? "60" : "24"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Envergure</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.armSpan}
                      onChange={(e) =>
                        handleInputChange("armSpan", e.target.value)
                      }
                      placeholder={unit === "cm" ? "170" : "67"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Tour de poignet</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.wristCircumference}
                      onChange={(e) =>
                        handleInputChange("wristCircumference", e.target.value)
                      }
                      placeholder={unit === "cm" ? "16" : "6"}
                    />
                  </div>
                </div>
              </div>

              {/* Leg Measurements */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6">
                  Mesures des Jambes ({unit})
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label className={labelClassName}>Entrejambe</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.inseam}
                      onChange={(e) =>
                        handleInputChange("inseam", e.target.value)
                      }
                      placeholder={unit === "cm" ? "80" : "32"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Tour de cuisse</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.thighCircumference}
                      onChange={(e) =>
                        handleInputChange("thighCircumference", e.target.value)
                      }
                      placeholder={unit === "cm" ? "55" : "22"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Tour de mollet</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.calfCircumference}
                      onChange={(e) =>
                        handleInputChange("calfCircumference", e.target.value)
                      }
                      placeholder={unit === "cm" ? "35" : "14"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Tour de cheville</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.ankleCircumference}
                      onChange={(e) =>
                        handleInputChange("ankleCircumference", e.target.value)
                      }
                      placeholder={unit === "cm" ? "22" : "9"}
                    />
                  </div>
                </div>
              </div>

              {/* Additional Measurements */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6">
                  Mesures Additionnelles ({unit})
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label className={labelClassName}>Largeur du dos</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.backWidth}
                      onChange={(e) =>
                        handleInputChange("backWidth", e.target.value)
                      }
                      placeholder={unit === "cm" ? "40" : "16"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Tour de tête</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.headCircumference}
                      onChange={(e) =>
                        handleInputChange("headCircumference", e.target.value)
                      }
                      placeholder={unit === "cm" ? "56" : "22"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Longueur du pied</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.footLength}
                      onChange={(e) =>
                        handleInputChange("footLength", e.target.value)
                      }
                      placeholder={unit === "cm" ? "26" : "10"}
                    />
                  </div>
                  <div>
                    <label className={labelClassName}>Largeur du pied</label>
                    <input
                      type="number"
                      step="0.1"
                      className={inputClassName}
                      value={measurements.footWidth}
                      onChange={(e) =>
                        handleInputChange("footWidth", e.target.value)
                      }
                      placeholder={unit === "cm" ? "10" : "4"}
                    />
                  </div>
                </div>
              </div>

              {/* Measurement Guide */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mb-6">
                <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4">
                  Guide de Mesure
                </h3>
                <div className="text-sm text-blue-700 dark:text-blue-300 space-y-2">
                  <p>
                    <strong>Tour de poitrine:</strong> Mesurez autour de la
                    partie la plus large de la poitrine
                  </p>
                  <p>
                    <strong>Tour de taille:</strong> Mesurez autour de la partie
                    la plus étroite de la taille
                  </p>
                  <p>
                    <strong>Tour de hanches:</strong> Mesurez autour de la
                    partie la plus large des hanches
                  </p>
                  <p>
                    <strong>Entrejambe:</strong> Mesurez de l'entrejambe
                    jusqu'au sol
                  </p>
                  <p>
                    <strong>Envergure:</strong> Mesurez d'un bout des doigts à
                    l'autre, bras étendus
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
                {submitMessage && (
                  <div
                    className={`mb-4 p-3 rounded-md ${
                      submitMessage.includes("succès")
                        ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                        : "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"
                    }`}
                  >
                    {submitMessage}
                  </div>
                )}

                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out"
                    onClick={() => {
                      setMeasurements({
                        firstName: "",
                        lastName: "",
                        gender: "male",
                        age: "",
                        height: "",
                        weight: "",
                        chest: "",
                        waist: "",
                        hips: "",
                        shoulderWidth: "",
                        neckCircumference: "",
                        armLength: "",
                        armSpan: "",
                        wristCircumference: "",
                        inseam: "",
                        thighCircumference: "",
                        calfCircumference: "",
                        ankleCircumference: "",
                        torsoLength: "",
                        backWidth: "",
                        headCircumference: "",
                        footLength: "",
                        footWidth: "",
                      });
                      setSubmitMessage("");
                    }}
                  >
                    Réinitialiser
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-violet-600 hover:bg-violet-700 disabled:bg-violet-400 text-white font-medium py-2 px-6 rounded-md transition duration-150 ease-in-out"
                  >
                    {isSubmitting
                      ? "Génération en cours..."
                      : "Générer le Modèle 3D"}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Account;
