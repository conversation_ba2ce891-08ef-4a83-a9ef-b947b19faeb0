
def create_smpl_model(gender):
    """Crée un modèle SMPL réel"""
    try:
        import torch
        from smpl_loader import create_smpl_model_safe
        
        model_path = os.path.join(settings.BASE_DIR, 'mannequin_project', 'models', 'smpl')
        
        # Essayer le chargeur sécurisé
        model = create_smpl_model_safe(model_path, gender, 10)
        
        if model is not None:
            logger.info(f"Modèle SMPL {gender} chargé avec succès")
            return model
        else:
            logger.warning(f"Impossible de charger le modèle SMPL {gender}")
            return None
            
    except Exception as e:
        logger.error(f"Erreur lors du chargement SMPL {gender}: {e}")
        return None

def generate_smpl_model_real(measurements, betas, gender):
    """Génère un modèle SMPL réel"""
    try:
        import torch
        
        # Charger le modèle SMPL
        smpl_model = create_smpl_model(gender)
        
        if smpl_model is None:
            return None
        
        # Convertir les betas en tensor
        betas_tensor = torch.tensor(betas, dtype=torch.float32).unsqueeze(0)
        
        # Générer le modèle
        if hasattr(smpl_model, '__call__'):
            # Modèle SMPL-X
            output = smpl_model(betas=betas_tensor)
            vertices = output.vertices.detach().numpy()[0]
            faces = smpl_model.faces
        else:
            # Données brutes - simulation basée sur les données SMPL
            vertices = smpl_model['v_template']
            faces = smpl_model['f']
            
            # Appliquer les déformations betas
            if 'shapedirs' in smpl_model:
                shapedirs = smpl_model['shapedirs']
                for i, beta in enumerate(betas[:min(len(betas), shapedirs.shape[2])]):
                    vertices += beta * shapedirs[:, :, i]
        
        logger.info(f"Modèle SMPL {gender} généré: {vertices.shape[0]} vertices, {faces.shape[0]} faces")
        
        return {
            'vertices': vertices.tolist(),
            'faces': faces.tolist(),
            'model_type': 'SMPL_REAL',
            'vertices_count': vertices.shape[0],
            'faces_count': faces.shape[0]
        }
        
    except Exception as e:
        logger.error(f"Erreur génération SMPL: {e}")
        return None
