import React, { useEffect } from "react";
import { Routes, Route, useLocation, Navigate } from "react-router-dom";

import "./css/style.css";

import "./charts/ChartjsConfig";

// Import pages
import Dashboard from "./pages/Dashboard";
import Analytics from "./pages/Analytics";
import Fintech from "./pages/Fintech";
import Messages from "./pages/Messages";
import Inbox from "./pages/Inbox";
import Calendar from "./pages/Calendar";
import Campaigns from "./pages/Campaigns";

// Import Ecommerce pages
import Customers from "./pages/Ecommerce/Customers";
import Orders from "./pages/Ecommerce/Orders";
import Invoices from "./pages/Ecommerce/Invoices";

// Import Task pages
import Kanban from "./pages/Tasks/Kanban";
import TaskList from "./pages/Tasks/List";

// Import Settings pages
import Account from "./pages/Settings/Account";
import Notifications from "./pages/Settings/Notifications";

function App() {
  const location = useLocation();

  useEffect(() => {
    document.querySelector("html").style.scrollBehavior = "auto";
    window.scroll({ top: 0 });
    document.querySelector("html").style.scrollBehavior = "";
  }, [location.pathname]); // triggered on route change

  return (
    <>
      <Routes>
        <Route exact path="/" element={<Dashboard />} />
        <Route path="/analytics" element={<Analytics />} />
        <Route path="/fintech" element={<Fintech />} />
        <Route path="/messages" element={<Messages />} />
        <Route path="/inbox" element={<Inbox />} />
        <Route path="/calendar" element={<Calendar />} />
        <Route path="/campaigns" element={<Campaigns />} />

        {/* Ecommerce Routes */}
        <Route path="/ecommerce/customers" element={<Customers />} />
        <Route path="/ecommerce/orders" element={<Orders />} />
        <Route path="/ecommerce/invoices" element={<Invoices />} />

        {/* Task Routes */}
        <Route path="/tasks/kanban" element={<Kanban />} />
        <Route path="/tasks/list" element={<TaskList />} />

        {/* 3D Body Measurements Route */}
        <Route path="/3d-body" element={<Account />} />
        {/* Legacy redirect for old route */}
        <Route
          path="/settings/account"
          element={<Navigate to="/3d-body" replace />}
        />
        <Route path="/settings/notifications" element={<Notifications />} />
      </Routes>
    </>
  );
}

export default App;
