#!/usr/bin/env python3
"""
Test simple de validation sans Django
"""

def safe_float(value, default=0.0):
    """Convertit une valeur en float de manière sécurisée"""
    if value is None or value == '' or value == 'undefined':
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        print(f"Impossible de convertir '{value}' en float, utilisation de la valeur par défaut {default}")
        return default

def test_safe_float():
    """Test de la fonction safe_float"""
    print("🧪 Test safe_float")
    print("=" * 30)
    
    test_cases = [
        ("", 10.0, 10.0, "chaîne vide"),
        (None, 10.0, 10.0, "None"),
        ("undefined", 10.0, 10.0, "undefined"),
        ("abc", 10.0, 10.0, "texte invalide"),
        ("123.45", 10.0, 123.45, "nombre valide"),
        (123.45, 10.0, 123.45, "float valide"),
        (0, 10.0, 0.0, "zéro"),
        ("0", 10.0, 0.0, "zéro string")
    ]
    
    success_count = 0
    
    for value, default, expected, description in test_cases:
        result = safe_float(value, default)
        if result == expected:
            print(f"✅ {description}: '{value}' -> {result}")
            success_count += 1
        else:
            print(f"❌ {description}: '{value}' -> {result} (attendu: {expected})")
    
    print(f"\nRésultat: {success_count}/{len(test_cases)} tests réussis")
    return success_count == len(test_cases)

def validate_and_clean_measurements(data):
    """Version simplifiée de validation"""
    
    # Mesures de base avec valeurs par défaut
    basic_measurements = {
        'height': safe_float(data.get("taille"), 170.0),
        'weight': safe_float(data.get("weight"), 70.0),
        'chest': safe_float(data.get("poitrine"), 90.0),
        'waist': safe_float(data.get("tour_taille"), 75.0),
        'hips': safe_float(data.get("hanches"), 95.0),
    }
    
    # Extraire les mesures additionnelles
    additional_measurements = data.get("additional_measurements", {})
    
    # Mesures additionnelles avec valeurs par défaut
    additional_clean = {
        'shoulder_width': safe_float(additional_measurements.get('shoulderWidth'), 45.0),
        'neck_circumference': safe_float(additional_measurements.get('neckCircumference'), 38.0),
        'arm_length': safe_float(additional_measurements.get('armLength'), 60.0),
        'arm_span': safe_float(additional_measurements.get('armSpan'), 170.0),
        'torso_length': safe_float(additional_measurements.get('torsoLength'), 60.0),
        'inseam': safe_float(additional_measurements.get('inseam'), 80.0),
        'thigh_circumference': safe_float(additional_measurements.get('thighCircumference'), 55.0),
        'calf_circumference': safe_float(additional_measurements.get('calfCircumference'), 35.0),
        'ankle_circumference': safe_float(additional_measurements.get('ankleCircumference'), 22.0),
        'back_width': safe_float(additional_measurements.get('backWidth'), 40.0),
        'head_circumference': safe_float(additional_measurements.get('headCircumference'), 56.0),
        'foot_length': safe_float(additional_measurements.get('footLength'), 26.0),
        'foot_width': safe_float(additional_measurements.get('footWidth'), 10.0),
        'wrist_circumference': safe_float(additional_measurements.get('wristCircumference'), 16.0),
    }
    
    # Combiner toutes les mesures
    all_measurements = {**basic_measurements, **additional_clean}
    
    # Validation des valeurs (limites raisonnables)
    all_measurements['height'] = max(100, min(250, all_measurements['height']))  # 1m à 2.5m
    all_measurements['weight'] = max(30, min(300, all_measurements['weight']))   # 30kg à 300kg
    all_measurements['chest'] = max(50, min(200, all_measurements['chest']))     # 50cm à 200cm
    all_measurements['waist'] = max(40, min(180, all_measurements['waist']))     # 40cm à 180cm
    all_measurements['hips'] = max(50, min(200, all_measurements['hips']))       # 50cm à 200cm
    
    return all_measurements

def test_validation():
    """Test de validation avec données problématiques"""
    print("\n📋 Test Validation Données Problématiques")
    print("=" * 50)
    
    # Données avec champs vides et valeurs invalides
    test_data = {
        "taille": "",           # Vide
        "weight": "abc",        # Invalide
        "poitrine": "95",       # Valide
        "tour_taille": "",      # Vide
        "hanches": "90",        # Valide
        "genre": "male",
        "additional_measurements": {
            "shoulderWidth": "",        # Vide
            "neckCircumference": "xyz", # Invalide
            "armLength": "65",          # Valide
            "armSpan": "",              # Vide
            "torsoLength": "60",        # Valide
            "inseam": "",               # Vide
            "thighCircumference": "55", # Valide
            "calfCircumference": "",    # Vide
            "ankleCircumference": "22", # Valide
            "backWidth": "",            # Vide
            "headCircumference": "56",  # Valide
            "footLength": "",           # Vide
            "footWidth": "10",          # Valide
            "wristCircumference": ""    # Vide
        }
    }
    
    print("Données d'entrée avec problèmes:")
    for key, value in test_data.items():
        if key != "additional_measurements":
            print(f"  {key}: '{value}'")
    
    print("Mesures additionnelles avec problèmes:")
    for key, value in test_data["additional_measurements"].items():
        print(f"  {key}: '{value}'")
    
    # Valider et nettoyer
    try:
        cleaned = validate_and_clean_measurements(test_data)
        
        print("\n✅ Données nettoyées avec succès:")
        for key, value in cleaned.items():
            print(f"  {key}: {value}")
        
        # Vérifications
        assert cleaned['height'] == 170.0, "Taille par défaut incorrecte"
        assert cleaned['weight'] == 70.0, "Poids par défaut incorrect"
        assert cleaned['chest'] == 95.0, "Poitrine incorrecte"
        assert cleaned['waist'] == 75.0, "Taille par défaut incorrecte"
        assert cleaned['hips'] == 90.0, "Hanches incorrectes"
        assert cleaned['shoulder_width'] == 45.0, "Largeur épaules par défaut incorrecte"
        assert cleaned['arm_length'] == 65.0, "Longueur bras correcte"
        
        print("\n✅ Toutes les validations sont correctes!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la validation: {e}")
        return False

def test_extreme_values():
    """Test avec valeurs extrêmes"""
    print("\n🔬 Test Valeurs Extrêmes")
    print("=" * 30)
    
    extreme_data = {
        "taille": "500",    # Trop grand
        "weight": "10",     # Trop petit
        "poitrine": "300",  # Trop grand
        "tour_taille": "5", # Trop petit
        "hanches": "400",   # Trop grand
    }
    
    cleaned = validate_and_clean_measurements(extreme_data)
    
    print("Valeurs extrêmes:")
    for key, value in extreme_data.items():
        print(f"  {key}: {value}")
    
    print("\nValeurs limitées:")
    for key, value in cleaned.items():
        if key in ['height', 'weight', 'chest', 'waist', 'hips']:
            print(f"  {key}: {value}")
    
    # Vérifier les limites
    assert 100 <= cleaned['height'] <= 250, f"Taille non limitée: {cleaned['height']}"
    assert 30 <= cleaned['weight'] <= 300, f"Poids non limité: {cleaned['weight']}"
    assert 50 <= cleaned['chest'] <= 200, f"Poitrine non limitée: {cleaned['chest']}"
    assert 40 <= cleaned['waist'] <= 180, f"Taille non limitée: {cleaned['waist']}"
    assert 50 <= cleaned['hips'] <= 200, f"Hanches non limitées: {cleaned['hips']}"
    
    print("✅ Limitation des valeurs extrêmes fonctionne!")
    return True

def main():
    """Fonction principale"""
    print("🚀 Tests de Validation - Correction Erreur Float")
    print("=" * 60)
    
    success_count = 0
    
    if test_safe_float():
        success_count += 1
        print("✅ Test safe_float réussi")
    
    if test_validation():
        success_count += 1
        print("✅ Test validation réussi")
    
    if test_extreme_values():
        success_count += 1
        print("✅ Test valeurs extrêmes réussi")
    
    print(f"\n📊 Résultats finaux: {success_count}/3 tests réussis")
    
    if success_count == 3:
        print("\n🎉 CORRECTION RÉUSSIE!")
        print("💡 L'erreur 'could not convert string to float' est corrigée")
        print("🔧 Le code gère maintenant:")
        print("   - Champs vides ('')")
        print("   - Valeurs None")
        print("   - Valeurs 'undefined'")
        print("   - Texte invalide")
        print("   - Valeurs extrêmes")
        print("\n🔗 Testez maintenant votre application!")
    else:
        print("\n⚠️ Certains tests ont échoué")

if __name__ == "__main__":
    main()
