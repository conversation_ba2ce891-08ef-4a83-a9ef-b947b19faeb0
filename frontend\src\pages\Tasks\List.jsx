import React from 'react';
import Sidebar from '../../partials/Sidebar';
import Header from '../../partials/Header';

function List() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  return (
    <div className="flex h-[100dvh] overflow-hidden">
      {/* Sidebar */}
      <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {/* Site header */}
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        <main className="grow">
          <div className="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
            {/* Page header */}
            <div className="mb-8">
              <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">Task List</h1>
            </div>

            {/* Content */}
            <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">All Tasks</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Manage your tasks in a simple list format.
              </p>
              
              <div className="space-y-3">
                <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <input type="checkbox" className="w-4 h-4 text-violet-600 bg-gray-100 border-gray-300 rounded focus:ring-violet-500 dark:focus:ring-violet-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" />
                  <div className="ml-4 flex-1">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">Complete project documentation</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Review and finalize all project documentation</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">High</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Dec 15</span>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <input type="checkbox" className="w-4 h-4 text-violet-600 bg-gray-100 border-gray-300 rounded focus:ring-violet-500 dark:focus:ring-violet-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" />
                  <div className="ml-4 flex-1">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">Update user interface</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Implement new design changes to the dashboard</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">Medium</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Dec 20</span>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg opacity-50">
                  <input type="checkbox" checked className="w-4 h-4 text-violet-600 bg-gray-100 border-gray-300 rounded focus:ring-violet-500 dark:focus:ring-violet-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" />
                  <div className="ml-4 flex-1">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 line-through">Setup development environment</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-through">Configure all necessary tools and dependencies</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Completed</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Dec 10</span>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <input type="checkbox" className="w-4 h-4 text-violet-600 bg-gray-100 border-gray-300 rounded focus:ring-violet-500 dark:focus:ring-violet-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" />
                  <div className="ml-4 flex-1">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">Code review session</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Review recent code changes with the team</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">Low</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Dec 25</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default List;
