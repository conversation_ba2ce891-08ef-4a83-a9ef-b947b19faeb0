#!/usr/bin/env python3
"""
Test SMPL simplifié sans Django
"""

import torch
import numpy as np

def test_smpl_simple():
    """Test SMPL simple"""
    print("🧪 Test SMPL Simple")
    print("=" * 40)
    
    try:
        import smplx
        print("✅ smplx importé")
    except ImportError as e:
        print(f"❌ Erreur import: {e}")
        return False
    
    try:
        print("🚹 Chargement modèle masculin...")
        
        model = smplx.create(
            model_path="mannequin_project/models/smpl",
            model_type='smpl',
            gender='male',
            num_betas=10,
            batch_size=1
        )
        
        print(f"✅ Modèle chargé!")
        print(f"   - Vertices: {model.get_num_verts():,}")
        print(f"   - Faces: {model.faces.shape[0]:,}")
        
        # Test avec des betas simples
        betas = torch.zeros(1, 10)  # Corps "moyen"
        betas[0, 0] = 0.5  # Un peu plus grand
        betas[0, 1] = 0.2  # Un peu plus large
        
        print(f"🧬 Betas de test: {betas[0].tolist()}")
        
        # Génération
        output = model(betas=betas)
        vertices = output.vertices.detach().numpy()[0]
        
        print(f"✅ Génération réussie!")
        print(f"   - Shape: {vertices.shape}")
        print(f"   - X: [{vertices[:, 0].min():.3f}, {vertices[:, 0].max():.3f}]")
        print(f"   - Y: [{vertices[:, 1].min():.3f}, {vertices[:, 1].max():.3f}]")
        print(f"   - Z: [{vertices[:, 2].min():.3f}, {vertices[:, 2].max():.3f}]")
        
        # Simuler la conversion de mesures
        print("\n📏 Test conversion mesures...")
        
        # Mesures de test (homme de 175cm, 75kg)
        height = 175.0
        weight = 75.0
        chest = 95.0
        waist = 80.0
        hips = 90.0
        
        # Conversion simple en betas
        betas_from_measurements = torch.zeros(1, 10)
        betas_from_measurements[0, 0] = (height - 170) / 20.0  # Taille
        betas_from_measurements[0, 1] = (chest - 90) / 15.0    # Largeur
        betas_from_measurements[0, 2] = (waist - 75) / 12.0    # Taille
        betas_from_measurements[0, 3] = (hips - 90) / 15.0     # Hanches
        
        print(f"📐 Mesures: H={height}, P={chest}, T={waist}, H={hips}")
        print(f"🧬 Betas calculés: {betas_from_measurements[0].tolist()}")
        
        # Génération avec mesures
        output2 = model(betas=betas_from_measurements)
        vertices2 = output2.vertices.detach().numpy()[0]
        
        print(f"✅ Modèle personnalisé généré!")
        print(f"   - Différence moyenne: {np.mean(np.abs(vertices2 - vertices)):.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_smpl_simple():
        print("\n🎉 SMPL fonctionne parfaitement!")
        print("💡 Prêt pour l'intégration dans l'application")
    else:
        print("\n❌ Problème avec SMPL")
        print("💡 Vérifiez l'installation")
