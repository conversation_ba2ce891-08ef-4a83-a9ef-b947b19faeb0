import React, { useRef, useEffect, useState } from "react";
import * as THREE from "three";
import { ModelExporter } from "../utils/modelExporter";

const Model3DViewer = ({ modelData, isVisible = true }) => {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const meshRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!isVisible || !mountRef.current) return;

    // Configuration de la scène
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    sceneRef.current = scene;

    // Configuration de la caméra
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 0, 3);

    // Configuration du renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(
      mountRef.current.clientWidth,
      mountRef.current.clientHeight
    );
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    // Éclairage
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.4);
    directionalLight2.position.set(-1, -1, -1);
    scene.add(directionalLight2);

    // Contrôles de la caméra (rotation avec la souris)
    let isMouseDown = false;
    let mouseX = 0;
    let mouseY = 0;
    let targetRotationX = 0;
    let targetRotationY = 0;
    let currentRotationX = 0;
    let currentRotationY = 0;

    const onMouseDown = (event) => {
      isMouseDown = true;
      mouseX = event.clientX;
      mouseY = event.clientY;
    };

    const onMouseUp = () => {
      isMouseDown = false;
    };

    const onMouseMove = (event) => {
      if (!isMouseDown) return;

      const deltaX = event.clientX - mouseX;
      const deltaY = event.clientY - mouseY;

      targetRotationY += deltaX * 0.01;
      targetRotationX += deltaY * 0.01;

      mouseX = event.clientX;
      mouseY = event.clientY;
    };

    const onWheel = (event) => {
      camera.position.z += event.deltaY * 0.01;
      camera.position.z = Math.max(1, Math.min(10, camera.position.z));
    };

    // Ajout des event listeners
    mountRef.current.addEventListener("mousedown", onMouseDown);
    mountRef.current.addEventListener("mouseup", onMouseUp);
    mountRef.current.addEventListener("mousemove", onMouseMove);
    mountRef.current.addEventListener("wheel", onWheel);

    // Ajout du canvas au DOM
    mountRef.current.appendChild(renderer.domElement);

    // Boucle d'animation
    const animate = () => {
      requestAnimationFrame(animate);

      // Rotation fluide
      currentRotationX += (targetRotationX - currentRotationX) * 0.1;
      currentRotationY += (targetRotationY - currentRotationY) * 0.1;

      if (meshRef.current) {
        meshRef.current.rotation.x = currentRotationX;
        meshRef.current.rotation.y = currentRotationY;
      }

      renderer.render(scene, camera);
    };

    animate();

    // Gestion du redimensionnement
    const handleResize = () => {
      if (!mountRef.current) return;

      camera.aspect =
        mountRef.current.clientWidth / mountRef.current.clientHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(
        mountRef.current.clientWidth,
        mountRef.current.clientHeight
      );
    };

    window.addEventListener("resize", handleResize);

    // Nettoyage
    return () => {
      if (mountRef.current) {
        mountRef.current.removeEventListener("mousedown", onMouseDown);
        mountRef.current.removeEventListener("mouseup", onMouseUp);
        mountRef.current.removeEventListener("mousemove", onMouseMove);
        mountRef.current.removeEventListener("wheel", onWheel);

        if (
          renderer.domElement &&
          mountRef.current.contains(renderer.domElement)
        ) {
          mountRef.current.removeChild(renderer.domElement);
        }
      }

      window.removeEventListener("resize", handleResize);
      renderer.dispose();
    };
  }, [isVisible]);

  // Chargement du modèle 3D
  useEffect(() => {
    if (!modelData || !sceneRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // Supprimer l'ancien mesh s'il existe
      if (meshRef.current) {
        sceneRef.current.remove(meshRef.current);
        meshRef.current = null;
      }

      // Créer la géométrie à partir des données du modèle
      const geometry = new THREE.BufferGeometry();

      if (modelData.vertices && modelData.faces) {
        // Modèle SMPL réel avec vertices et faces
        const vertices = new Float32Array(modelData.vertices.flat());
        const indices = new Uint32Array(modelData.faces.flat());

        geometry.setAttribute(
          "position",
          new THREE.BufferAttribute(vertices, 3)
        );
        geometry.setIndex(new THREE.BufferAttribute(indices, 1));
        geometry.computeVertexNormals();

        // Centrer le modèle
        geometry.computeBoundingBox();
        const center = new THREE.Vector3();
        geometry.boundingBox.getCenter(center);
        geometry.translate(-center.x, -center.y, -center.z);

        // Normaliser la taille
        const size = new THREE.Vector3();
        geometry.boundingBox.getSize(size);
        const maxDimension = Math.max(size.x, size.y, size.z);
        const scale = 2 / maxDimension;
        geometry.scale(scale, scale, scale);
      } else {
        // Modèle de simulation - créer une forme humanoïde basique
        createHumanoidGeometry(geometry, modelData);
      }

      // Matériau
      const material = new THREE.MeshLambertMaterial({
        color: 0x8b4513,
        side: THREE.DoubleSide,
        wireframe: false,
      });

      // Créer le mesh
      const mesh = new THREE.Mesh(geometry, material);
      mesh.castShadow = true;
      mesh.receiveShadow = true;
      meshRef.current = mesh;

      // Ajouter à la scène
      sceneRef.current.add(mesh);

      setIsLoading(false);
    } catch (err) {
      console.error("Erreur lors du chargement du modèle 3D:", err);
      setError("Erreur lors du chargement du modèle 3D");
      setIsLoading(false);
    }
  }, [modelData]);

  const createHumanoidGeometry = (geometry, modelData) => {
    // Créer une forme humanoïde simplifiée basée sur les mesures
    const measurements = modelData.measurements || {};

    // Proportions basées sur les mesures
    const height = (measurements.height || 170) / 170; // Normaliser à 170cm
    const width = (measurements.chest || 90) / 90; // Normaliser à 90cm
    const depth = (measurements.waist || 75) / 75; // Normaliser à 75cm

    // Créer une forme de base (ellipsoïde allongé)
    const segments = 32;
    const vertices = [];
    const faces = [];

    // Générer les vertices pour une forme humanoïde simplifiée
    for (let i = 0; i <= segments; i++) {
      const theta = (i / segments) * Math.PI * 2;

      for (let j = 0; j <= segments; j++) {
        const phi = (j / segments) * Math.PI;

        // Forme ellipsoïdale avec variations pour simuler un corps humain
        let x = Math.sin(phi) * Math.cos(theta) * width * 0.3;
        let y = (Math.cos(phi) - 0.5) * height * 0.8;
        let z = Math.sin(phi) * Math.sin(theta) * depth * 0.2;

        // Ajustements pour une forme plus humaine
        if (y > 0.2) {
          // Partie supérieure plus large (torse)
          x *= 1.2;
          z *= 1.1;
        } else if (y < -0.3) {
          // Partie inférieure (jambes)
          x *= 0.6;
          z *= 0.8;
        }

        vertices.push(x, y, z);
      }
    }

    // Générer les faces
    for (let i = 0; i < segments; i++) {
      for (let j = 0; j < segments; j++) {
        const a = i * (segments + 1) + j;
        const b = a + segments + 1;
        const c = a + 1;
        const d = b + 1;

        faces.push(a, b, c);
        faces.push(b, d, c);
      }
    }

    geometry.setAttribute(
      "position",
      new THREE.Float32BufferAttribute(vertices, 3)
    );
    geometry.setIndex(faces);
    geometry.computeVertexNormals();
  };

  const exportModel = (format) => {
    if (!modelData) return;

    try {
      const exporter = new ModelExporter(modelData);
      const result = exporter.export(format);

      // Afficher un message de succès
      console.log(
        `Modèle exporté: ${result.filename} (${(result.size / 1024).toFixed(
          1
        )} KB)`
      );
    } catch (err) {
      console.error("Erreur lors de l'export:", err);
      setError("Erreur lors de l'export du modèle");
    }
  };

  // Les fonctions d'export sont maintenant gérées par ModelExporter

  if (!isVisible) return null;

  return (
    <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
          Visualisation 3D
        </h3>

        {modelData && (
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => exportModel("obj")}
              className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition duration-150"
              title="Exporter en format OBJ (Wavefront)"
            >
              📄 OBJ
            </button>
            <button
              onClick={() => exportModel("ply")}
              className="px-3 py-1 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md transition duration-150"
              title="Exporter en format PLY (Stanford)"
            >
              📐 PLY
            </button>
            <button
              onClick={() => exportModel("stl")}
              className="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 text-white rounded-md transition duration-150"
              title="Exporter en format STL (Stereolithography)"
            >
              🖨️ STL
            </button>
            <button
              onClick={() => exportModel("json")}
              className="px-3 py-1 text-sm bg-purple-600 hover:bg-purple-700 text-white rounded-md transition duration-150"
              title="Exporter les données JSON complètes"
            >
              📊 JSON
            </button>
            <button
              onClick={() => exportModel("csv")}
              className="px-3 py-1 text-sm bg-orange-600 hover:bg-orange-700 text-white rounded-md transition duration-150"
              title="Exporter les mesures en CSV"
            >
              📋 CSV
            </button>
          </div>
        )}
      </div>

      <div className="relative">
        <div
          ref={mountRef}
          className="w-full h-96 border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-700"
          style={{ cursor: modelData ? "grab" : "default" }}
        />

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 dark:bg-gray-800 dark:bg-opacity-75">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600 mx-auto mb-2"></div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Chargement du modèle 3D...
              </p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-red-50 dark:bg-red-900/20">
            <div className="text-center text-red-600 dark:text-red-400">
              <p className="font-medium">Erreur</p>
              <p className="text-sm">{error}</p>
            </div>
          </div>
        )}

        {!modelData && !isLoading && !error && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <div className="text-4xl mb-2">🎭</div>
              <p className="font-medium">Aucun modèle 3D</p>
              <p className="text-sm">
                Générez un modèle pour le visualiser ici
              </p>
            </div>
          </div>
        )}
      </div>

      {modelData && (
        <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
          <p>
            💡 <strong>Contrôles:</strong> Clic + glisser pour tourner, molette
            pour zoomer
          </p>
          <p>
            📊 <strong>Modèle:</strong> {modelData.model_type} -{" "}
            {modelData.vertices_count?.toLocaleString()} vertices
          </p>
        </div>
      )}
    </div>
  );
};

export default Model3DViewer;
