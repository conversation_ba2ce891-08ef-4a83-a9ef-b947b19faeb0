#!/usr/bin/env python3
"""
Test final SMPL avec chargeur personnalisé
"""

import torch
import numpy as np
from smpl_loader import create_smpl_model_safe

def test_smpl_final():
    """Test final de SMPL avec toutes les méthodes"""
    print("🎯 Test Final SMPL")
    print("=" * 50)
    
    model_path = "mannequin_project/models/smpl"
    
    # Test 1: Méthode standard SMPL-X
    print("🔬 Test 1: SMPL-X Standard")
    try:
        import smplx
        
        model = smplx.create(
            model_path=model_path,
            model_type='smpl',
            gender='male',
            num_betas=10,
            batch_size=1,
            use_face_contour=False,
            use_pca=False
        )
        
        print("✅ SMPL-X Standard fonctionne!")
        
        # Test de génération
        betas = torch.randn(1, 10) * 0.3
        output = model(betas=betas)
        vertices = output.vertices.detach().numpy()[0]
        
        print(f"   - Vertices: {vertices.shape}")
        print(f"   - Faces: {model.faces.shape}")
        print(f"   - Bounding box: X[{vertices[:, 0].min():.3f}, {vertices[:, 0].max():.3f}]")
        
        return True, "SMPL_STANDARD", model
        
    except Exception as e:
        print(f"❌ SMPL-X Standard échoué: {e}")
    
    # Test 2: Chargeur personnalisé
    print("\n🔧 Test 2: Chargeur Personnalisé")
    try:
        model = create_smpl_model_safe(model_path, 'male', 10)
        
        if model is not None:
            print("✅ Chargeur personnalisé fonctionne!")
            
            if hasattr(model, '__call__'):
                # C'est un modèle SMPL-X
                betas = torch.randn(1, 10) * 0.3
                output = model(betas=betas)
                vertices = output.vertices.detach().numpy()[0]
                print(f"   - Type: Modèle SMPL-X")
                print(f"   - Vertices: {vertices.shape}")
            else:
                # C'est des données brutes
                print(f"   - Type: Données brutes")
                print(f"   - Template vertices: {model.get('v_template', 'N/A')}")
                print(f"   - Faces: {model.get('f', 'N/A')}")
            
            return True, "SMPL_CUSTOM", model
        
    except Exception as e:
        print(f"❌ Chargeur personnalisé échoué: {e}")
    
    print("\n⚠️ SMPL réel non disponible, utilisation de la simulation")
    return False, "SIMULATION", None

def integrate_smpl_in_django():
    """Intègre SMPL dans le code Django"""
    print("\n🔗 Intégration SMPL dans Django")
    print("=" * 50)
    
    # Modifier le code Django pour utiliser SMPL
    django_code = '''
def create_smpl_model(gender):
    """Crée un modèle SMPL réel"""
    try:
        import torch
        from smpl_loader import create_smpl_model_safe
        
        model_path = os.path.join(settings.BASE_DIR, 'mannequin_project', 'models', 'smpl')
        
        # Essayer le chargeur sécurisé
        model = create_smpl_model_safe(model_path, gender, 10)
        
        if model is not None:
            logger.info(f"Modèle SMPL {gender} chargé avec succès")
            return model
        else:
            logger.warning(f"Impossible de charger le modèle SMPL {gender}")
            return None
            
    except Exception as e:
        logger.error(f"Erreur lors du chargement SMPL {gender}: {e}")
        return None

def generate_smpl_model_real(measurements, betas, gender):
    """Génère un modèle SMPL réel"""
    try:
        import torch
        
        # Charger le modèle SMPL
        smpl_model = create_smpl_model(gender)
        
        if smpl_model is None:
            return None
        
        # Convertir les betas en tensor
        betas_tensor = torch.tensor(betas, dtype=torch.float32).unsqueeze(0)
        
        # Générer le modèle
        if hasattr(smpl_model, '__call__'):
            # Modèle SMPL-X
            output = smpl_model(betas=betas_tensor)
            vertices = output.vertices.detach().numpy()[0]
            faces = smpl_model.faces
        else:
            # Données brutes - simulation basée sur les données SMPL
            vertices = smpl_model['v_template']
            faces = smpl_model['f']
            
            # Appliquer les déformations betas
            if 'shapedirs' in smpl_model:
                shapedirs = smpl_model['shapedirs']
                for i, beta in enumerate(betas[:min(len(betas), shapedirs.shape[2])]):
                    vertices += beta * shapedirs[:, :, i]
        
        logger.info(f"Modèle SMPL {gender} généré: {vertices.shape[0]} vertices, {faces.shape[0]} faces")
        
        return {
            'vertices': vertices.tolist(),
            'faces': faces.tolist(),
            'model_type': 'SMPL_REAL',
            'vertices_count': vertices.shape[0],
            'faces_count': faces.shape[0]
        }
        
    except Exception as e:
        logger.error(f"Erreur génération SMPL: {e}")
        return None
'''
    
    # Sauvegarder le code d'intégration
    with open('smpl_integration.py', 'w', encoding='utf-8') as f:
        f.write(django_code)
    
    print("✅ Code d'intégration Django créé: smpl_integration.py")

def update_django_views():
    """Met à jour les vues Django pour utiliser SMPL"""
    print("\n📝 Mise à jour des vues Django")
    print("=" * 50)
    
    # Instructions pour modifier views.py
    instructions = '''
# Instructions pour intégrer SMPL dans generateur/views.py

1. Ajouter en haut du fichier:
   from smpl_loader import create_smpl_model_safe
   import torch

2. Remplacer la fonction create_smpl_model par:
   
   def create_smpl_model(gender):
       """Crée un modèle SMPL réel"""
       try:
           model_path = os.path.join(settings.BASE_DIR, 'mannequin_project', 'models', 'smpl')
           model = create_smpl_model_safe(model_path, gender, 10)
           
           if model is not None:
               logger.info(f"Modèle SMPL {gender} chargé avec succès")
               return model
           else:
               logger.warning(f"Impossible de charger le modèle SMPL {gender}")
               return None
               
       except Exception as e:
           logger.error(f"Erreur lors du chargement SMPL {gender}: {e}")
           return None

3. Dans la fonction generate_3d_body, remplacer:
   
   # Utiliser notre simulation humanoïde avancée
   return generate_advanced_humanoid_model(all_measurements, betas, gender)
   
   Par:
   
   # Essayer d'utiliser SMPL réel
   smpl_result = generate_smpl_model_real(all_measurements, betas, gender)
   if smpl_result:
       return JsonResponse(smpl_result)
   else:
       # Fallback vers simulation
       return generate_advanced_humanoid_model(all_measurements, betas, gender)

4. Ajouter la fonction generate_smpl_model_real (voir smpl_integration.py)
'''
    
    with open('SMPL_INTEGRATION_INSTRUCTIONS.md', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Instructions créées: SMPL_INTEGRATION_INSTRUCTIONS.md")

def main():
    """Fonction principale"""
    print("🚀 Test Final SMPL et Intégration")
    print("=" * 60)
    
    # Test SMPL
    success, method, model = test_smpl_final()
    
    if success:
        print(f"\n🎉 SMPL fonctionne avec la méthode: {method}")
        
        # Intégration Django
        integrate_smpl_in_django()
        update_django_views()
        
        print("\n📋 Prochaines étapes:")
        print("1. Copiez le code de smpl_integration.py dans generateur/views.py")
        print("2. Suivez les instructions dans SMPL_INTEGRATION_INSTRUCTIONS.md")
        print("3. Redémarrez le serveur Django")
        print("4. Testez sur http://localhost:5174/3d-body")
        
        print(f"\n✅ SMPL est prêt à être utilisé!")
        
    else:
        print(f"\n⚠️ SMPL non disponible, utilisation de la simulation avancée")
        print("💡 La simulation humanoïde fonctionne très bien aussi!")

if __name__ == "__main__":
    main()
