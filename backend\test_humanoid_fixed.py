#!/usr/bin/env python3
"""
Test du modèle humanoïde corrigé
"""

import sys
import numpy as np

# A<PERSON>ter le répertoire courant au path
sys.path.append('.')

def create_humanoid_model_test(measurements, gender):
    """Version de test de create_humanoid_model"""
    
    # Proportions de base pour un corps humain (en mètres)
    height = measurements.get('height', 170) / 100.0
    chest_width = measurements.get('chest', 90) / 200.0  # Demi-largeur
    waist_width = measurements.get('waist', 75) / 200.0  # Demi-largeur
    hip_width = measurements.get('hips', 95) / 200.0     # Demi-largeur
    shoulder_width = measurements.get('shoulder_width', 45) / 200.0  # Demi-largeur

    # Ajustements selon le genre
    if gender == 'female':
        shoulder_width *= 0.9
        hip_width *= 1.1
        chest_width *= 0.95
    elif gender == 'male':
        shoulder_width *= 1.1
        hip_width *= 0.9
        chest_width *= 1.05

    # <PERSON><PERSON>er un modèle humanoïde réaliste
    vertices = []
    faces = []

    # Segments du corps avec proportions réalistes
    segments = [
        # [y_ratio_start, y_ratio_end, width_factor, depth_factor, name]
        [0.0, 0.08, 0.12, 0.08, "pieds"],
        [0.08, 0.45, 0.15, 0.12, "jambes"],
        [0.45, 0.58, 0.25, 0.18, "hanches"],
        [0.58, 0.72, 0.22, 0.16, "taille"],
        [0.72, 0.88, 0.28, 0.20, "torse"],
        [0.88, 0.95, 0.15, 0.12, "cou"],
        [0.95, 1.0, 0.18, 0.15, "tête"]
    ]

    # Générer les vertices pour chaque segment
    vertex_index = 0
    points_per_level = 12  # Nombre de points par niveau

    for segment in segments:
        y_start_ratio, y_end_ratio, width_factor, depth_factor, name = segment

        # Calculer les dimensions réelles
        y_start = y_start_ratio * height - height/2  # Centrer verticalement
        y_end = y_end_ratio * height - height/2

        # Ajuster les largeurs selon le segment
        if name in ['hanches', 'torse']:
            width = max(chest_width, hip_width) * width_factor
            depth = max(chest_width, hip_width) * depth_factor * 0.6
        else:
            width = shoulder_width * width_factor
            depth = shoulder_width * depth_factor * 0.6

        # Créer plusieurs niveaux dans chaque segment
        levels = 3
        for level in range(levels + 1):
            t = level / levels
            y = y_start + t * (y_end - y_start)

            # Créer un cercle de points à ce niveau
            for i in range(points_per_level):
                angle = 2 * np.pi * i / points_per_level
                x = width * np.cos(angle)
                z = depth * np.sin(angle)

                vertices.append([x, y, z])

            # Créer les faces pour connecter ce niveau au précédent
            if level > 0:
                current_start = vertex_index
                prev_start = vertex_index - points_per_level

                for i in range(points_per_level):
                    next_i = (i + 1) % points_per_level

                    # Créer deux triangles pour former un quad
                    faces.append([
                        prev_start + i,
                        current_start + i,
                        current_start + next_i
                    ])
                    faces.append([
                        prev_start + i,
                        current_start + next_i,
                        prev_start + next_i
                    ])

            vertex_index += points_per_level

    # Ajouter des bras simplifiés
    arm_length = measurements.get('arm_length', 60) / 100.0 * 0.8
    arm_radius = 0.03
    shoulder_y = height * 0.8 - height/2

    # Bras gauche et droit
    for side in [-1, 1]:  # Gauche (-1) et droite (1)
        arm_start_idx = len(vertices)

        # Créer 5 segments pour chaque bras
        for seg in range(6):
            t = seg / 5.0
            x = side * (shoulder_width * 1.2 + t * arm_length)
            y = shoulder_y - t * 0.1  # Légère pente vers le bas

            # Créer une section circulaire
            for p in range(6):
                angle = 2 * np.pi * p / 6
                arm_y = y + arm_radius * np.cos(angle)
                arm_z = arm_radius * np.sin(angle)
                vertices.append([x, arm_y, arm_z])

            # Connecter les segments
            if seg > 0:
                current_start = arm_start_idx + seg * 6
                prev_start = arm_start_idx + (seg - 1) * 6

                for p in range(6):
                    next_p = (p + 1) % 6

                    faces.append([prev_start + p, current_start + p, current_start + next_p])
                    faces.append([prev_start + p, current_start + next_p, prev_start + next_p])

    return vertices, faces

def test_humanoid_model():
    """Test du modèle humanoïde"""
    print("🧑 Test Modèle Humanoïde Corrigé")
    print("=" * 50)
    
    # Mesures de test
    measurements = {
        'height': 175.0,
        'weight': 70.0,
        'chest': 95.0,
        'waist': 80.0,
        'hips': 90.0,
        'shoulder_width': 45.0,
        'arm_length': 65.0
    }
    
    gender = 'male'
    
    print(f"📏 Mesures: {measurements}")
    print(f"👤 Genre: {gender}")
    
    # Créer le modèle
    vertices, faces = create_humanoid_model_test(measurements, gender)
    
    vertices = np.array(vertices)
    faces = np.array(faces)
    
    print(f"\n📊 Modèle généré:")
    print(f"   - Vertices: {vertices.shape}")
    print(f"   - Faces: {faces.shape}")
    
    # Analyser les proportions
    x_range = vertices[:, 0].max() - vertices[:, 0].min()
    y_range = vertices[:, 1].max() - vertices[:, 1].min()
    z_range = vertices[:, 2].max() - vertices[:, 2].min()
    
    print(f"\n📐 Dimensions:")
    print(f"   - Largeur (X): {x_range:.3f}m")
    print(f"   - Hauteur (Y): {y_range:.3f}m")
    print(f"   - Profondeur (Z): {z_range:.3f}m")
    
    # Vérifier les proportions humanoïdes
    height_width_ratio = y_range / x_range
    height_depth_ratio = y_range / z_range
    
    print(f"\n📊 Ratios:")
    print(f"   - Hauteur/Largeur: {height_width_ratio:.2f}")
    print(f"   - Hauteur/Profondeur: {height_depth_ratio:.2f}")
    
    # Vérifications
    if height_width_ratio > 2.0 and height_depth_ratio > 3.0:
        print("✅ Proportions humanoïdes correctes!")
        success = True
    else:
        print("⚠️ Proportions à améliorer")
        success = False
    
    # Vérifier la position verticale
    y_min = vertices[:, 1].min()
    y_max = vertices[:, 1].max()
    y_center = (y_min + y_max) / 2
    
    print(f"\n📍 Position verticale:")
    print(f"   - Y min: {y_min:.3f}m")
    print(f"   - Y max: {y_max:.3f}m")
    print(f"   - Y centre: {y_center:.3f}m")
    
    if abs(y_center) < 0.1:  # Centré autour de 0
        print("✅ Modèle bien centré verticalement")
    else:
        print("⚠️ Modèle décentré verticalement")
    
    # Vérifier les indices des faces
    max_vertex_idx = vertices.shape[0] - 1
    max_face_idx = faces.max()
    
    print(f"\n🔺 Validation des faces:")
    print(f"   - Max vertex index: {max_vertex_idx}")
    print(f"   - Max face index: {max_face_idx}")
    
    if max_face_idx <= max_vertex_idx:
        print("✅ Indices des faces valides")
    else:
        print("❌ Indices des faces invalides!")
        success = False
    
    return success, vertices, faces

def main():
    """Fonction principale"""
    print("🚀 Test Modèle Humanoïde - Correction Forme")
    print("=" * 60)
    
    success, vertices, faces = test_humanoid_model()
    
    if success:
        print("\n🎉 MODÈLE HUMANOÏDE CORRIGÉ AVEC SUCCÈS!")
        print("💡 Le modèle a maintenant des proportions humaines correctes")
        print("🔗 Testez maintenant votre application!")
        
        # Sauvegarder pour inspection
        np.save('humanoid_vertices_fixed.npy', vertices)
        np.save('humanoid_faces_fixed.npy', faces)
        print("\n💾 Modèle sauvegardé:")
        print("   - humanoid_vertices_fixed.npy")
        print("   - humanoid_faces_fixed.npy")
        
    else:
        print("\n⚠️ Le modèle nécessite encore des ajustements")
    
    print(f"\n📋 Prochaines étapes:")
    print(f"1. Testez l'application: http://localhost:5174/3d-body")
    print(f"2. Le modèle devrait maintenant ressembler à un corps humain")
    print(f"3. Plus de boule épineuse!")

if __name__ == "__main__":
    main()
