#!/usr/bin/env python3
"""
Test direct de l'API SMPL sans Django
"""

import sys
import os
import numpy as np

# Ajouter le répertoire courant au path
sys.path.append('.')

def test_smpl_integration():
    """Test de l'intégration SMPL"""
    print("🧪 Test Intégration SMPL Direct")
    print("=" * 50)
    
    try:
        # Import des modules nécessaires
        from smpl_loader import create_smpl_model_safe
        import torch
        
        # Mesures de test
        measurements = {
            'height': 175.0,
            'weight': 70.0,
            'chest': 95.0,
            'waist': 80.0,
            'hips': 90.0,
            'shoulder_width': 45.0,
            'arm_length': 65.0,
            'inseam': 82.0
        }
        
        print(f"📏 Mesures de test: {measurements}")
        
        # Simulation de la conversion betas (fonction simplifiée)
        def simple_measurements_to_betas(measurements):
            betas = np.zeros(10)
            betas[0] = (measurements.get('height', 170) - 170) / 20.0
            betas[1] = (measurements.get('chest', 90) - 90) / 15.0
            betas[2] = (measurements.get('waist', 75) - 75) / 12.0
            betas[3] = (measurements.get('hips', 90) - 90) / 15.0
            betas[4] = (measurements.get('shoulder_width', 45) - 45) / 10.0
            return np.clip(betas, -3.0, 3.0)
        
        betas = simple_measurements_to_betas(measurements)
        print(f"🧬 Betas calculés: {betas}")
        
        # Test de chargement du modèle SMPL
        model_path = "mannequin_project/models/smpl"
        gender = 'male'
        
        print(f"\n🔧 Chargement modèle SMPL {gender}...")
        smpl_model = create_smpl_model_safe(model_path, gender, 10)
        
        if smpl_model is None:
            print("❌ Modèle SMPL non chargé")
            return False
        
        print("✅ Modèle SMPL chargé avec succès!")
        
        # Test de génération
        print("\n🎯 Génération du modèle 3D...")
        
        # Convertir les betas en tensor
        betas_tensor = torch.tensor(betas, dtype=torch.float32).unsqueeze(0)
        
        # Générer le modèle
        if hasattr(smpl_model, '__call__'):
            # Modèle SMPL-X standard
            print("   - Type: Modèle SMPL-X standard")
            output = smpl_model(betas=betas_tensor)
            vertices = output.vertices.detach().numpy()[0]
            faces = smpl_model.faces.numpy()
            model_type = "SMPL_REAL"
        else:
            # Données brutes - simulation basée sur les données SMPL
            print("   - Type: Données SMPL brutes avec déformations")
            vertices = smpl_model['v_template'].copy()
            faces = smpl_model['f']
            
            # Appliquer les déformations betas
            if 'shapedirs' in smpl_model:
                shapedirs = smpl_model['shapedirs']
                for i, beta in enumerate(betas[:min(len(betas), shapedirs.shape[2])]):
                    vertices += beta * shapedirs[:, :, i]
            
            model_type = "SMPL_CUSTOM"
        
        print(f"✅ Modèle généré avec succès!")
        print(f"   - Type: {model_type}")
        print(f"   - Vertices: {vertices.shape[0]:,}")
        print(f"   - Faces: {faces.shape[0]:,}")
        print(f"   - Bounding box X: [{vertices[:, 0].min():.3f}, {vertices[:, 0].max():.3f}]")
        print(f"   - Bounding box Y: [{vertices[:, 1].min():.3f}, {vertices[:, 1].max():.3f}]")
        print(f"   - Bounding box Z: [{vertices[:, 2].min():.3f}, {vertices[:, 2].max():.3f}]")
        
        # Vérifier que le modèle a une forme humanoïde
        height_model = vertices[:, 1].max() - vertices[:, 1].min()
        width_model = vertices[:, 0].max() - vertices[:, 0].min()
        
        print(f"   - Hauteur modèle: {height_model:.3f}")
        print(f"   - Largeur modèle: {width_model:.3f}")
        print(f"   - Ratio H/L: {height_model/width_model:.2f}")
        
        if height_model / width_model > 2:
            print("✅ Proportions humanoïdes correctes!")
        else:
            print("⚠️ Proportions à vérifier")
        
        # Simulation de la réponse JSON
        result = {
            "success": True,
            "message": "Modèle 3D SMPL réel généré avec succès",
            "model_type": model_type,
            "vertices_count": int(vertices.shape[0]),
            "faces_count": int(faces.shape[0]),
            "gender": gender,
            "measurements": measurements,
            "betas": betas.tolist(),
            "vertices": vertices.tolist()[:100],  # Limiter pour l'affichage
            "faces": faces.tolist()[:100],        # Limiter pour l'affichage
            "model_info": {
                "smpl_version": "SMPL Réel",
                "num_betas": len(betas),
                "has_real_model": True,
                "note": "Modèle SMPL authentique basé sur vos mesures réelles",
                "quality": "Qualité SMPL - Standard de l'industrie"
            }
        }
        
        print(f"\n📊 Résultat de l'API:")
        print(f"   - Success: {result['success']}")
        print(f"   - Model type: {result['model_type']}")
        print(f"   - Vertices count: {result['vertices_count']:,}")
        print(f"   - Faces count: {result['faces_count']:,}")
        print(f"   - Has real model: {result['model_info']['has_real_model']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_genders():
    """Test avec différents genres"""
    print("\n👫 Test Différents Genres SMPL")
    print("=" * 50)
    
    try:
        from smpl_loader import create_smpl_model_safe
        
        model_path = "mannequin_project/models/smpl"
        
        for gender in ['male', 'female']:
            print(f"\n🧑 Test genre: {gender}")
            
            model = create_smpl_model_safe(model_path, gender, 10)
            
            if model is not None:
                if hasattr(model, '__call__'):
                    print(f"   - Type: Modèle SMPL-X")
                    print(f"   - Vertices: {model.get_num_verts():,}")
                    print(f"   - Faces: {model.faces.shape[0]:,}")
                else:
                    print(f"   - Type: Données SMPL brutes")
                    print(f"   - Template vertices: {model['v_template'].shape}")
                    print(f"   - Faces: {model['f'].shape}")
                
                print(f"   ✅ Modèle {gender} fonctionnel")
            else:
                print(f"   ❌ Modèle {gender} non chargé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test Complet SMPL Intégré")
    print("=" * 60)
    
    success_count = 0
    
    if test_smpl_integration():
        success_count += 1
        print("\n✅ Test d'intégration réussi!")
    
    if test_different_genders():
        success_count += 1
        print("\n✅ Test multi-genres réussi!")
    
    print(f"\n📊 Résultats: {success_count}/2 tests réussis")
    
    if success_count == 2:
        print("\n🎉 SMPL est complètement intégré et fonctionnel!")
        print("💡 Votre application peut maintenant utiliser les vrais modèles SMPL")
        print("🔗 Testez sur: http://localhost:5174/3d-body")
        print("\n📋 Prochaines étapes:")
        print("1. Démarrez le serveur Django: python manage.py runserver")
        print("2. Démarrez le frontend: npm run dev")
        print("3. Testez la génération de modèles SMPL réels")
    else:
        print("\n⚠️ Certains tests ont échoué")
        print("💡 Vérifiez l'installation SMPL et les fichiers de modèles")

if __name__ == "__main__":
    main()
