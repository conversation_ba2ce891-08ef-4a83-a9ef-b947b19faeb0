/**
 * Utilitaires d'export de modèles 3D
 * Supporte les formats OBJ, PLY, STL, et JSON
 */

export class ModelExporter {
  constructor(modelData) {
    this.modelData = modelData;
    this.vertices = modelData.vertices || [];
    this.faces = modelData.faces || [];
    this.measurements = modelData.measurements || {};
    this.betas = modelData.betas || [];
  }

  /**
   * Export au format OBJ (Wavefront)
   */
  exportOBJ() {
    let obj = '# Modèle 3D généré par l\'application de mesures corporelles\n';
    obj += `# Date: ${new Date().toISOString()}\n`;
    obj += `# Type: ${this.modelData.model_type || 'Unknown'}\n`;
    obj += `# Vertices: ${this.vertices.length / 3}\n`;
    obj += `# Faces: ${this.faces.length / 3}\n`;
    obj += '\n';

    // Ajouter les mesures en commentaires
    obj += '# Mesures corporelles:\n';
    Object.entries(this.measurements).forEach(([key, value]) => {
      obj += `# ${key}: ${value}\n`;
    });
    obj += '\n';

    // Vertices
    obj += '# Vertices\n';
    for (let i = 0; i < this.vertices.length; i += 3) {
      const x = this.vertices[i];
      const y = this.vertices[i + 1];
      const z = this.vertices[i + 2];
      obj += `v ${x.toFixed(6)} ${y.toFixed(6)} ${z.toFixed(6)}\n`;
    }

    // Faces (OBJ utilise des indices basés sur 1)
    obj += '\n# Faces\n';
    for (let i = 0; i < this.faces.length; i += 3) {
      const a = this.faces[i] + 1;
      const b = this.faces[i + 1] + 1;
      const c = this.faces[i + 2] + 1;
      obj += `f ${a} ${b} ${c}\n`;
    }

    return obj;
  }

  /**
   * Export au format PLY (Stanford)
   */
  exportPLY() {
    const vertexCount = this.vertices.length / 3;
    const faceCount = this.faces.length / 3;

    let ply = 'ply\n';
    ply += 'format ascii 1.0\n';
    ply += `comment Modèle 3D généré le ${new Date().toISOString()}\n`;
    ply += `comment Type: ${this.modelData.model_type || 'Unknown'}\n`;
    ply += `comment Genre: ${this.measurements.gender || 'Unknown'}\n`;
    ply += `comment Taille: ${this.measurements.height || 'Unknown'} cm\n`;
    ply += `element vertex ${vertexCount}\n`;
    ply += 'property float x\n';
    ply += 'property float y\n';
    ply += 'property float z\n';
    ply += `element face ${faceCount}\n`;
    ply += 'property list uchar int vertex_indices\n';
    ply += 'end_header\n';

    // Vertices
    for (let i = 0; i < this.vertices.length; i += 3) {
      const x = this.vertices[i];
      const y = this.vertices[i + 1];
      const z = this.vertices[i + 2];
      ply += `${x.toFixed(6)} ${y.toFixed(6)} ${z.toFixed(6)}\n`;
    }

    // Faces
    for (let i = 0; i < this.faces.length; i += 3) {
      const a = this.faces[i];
      const b = this.faces[i + 1];
      const c = this.faces[i + 2];
      ply += `3 ${a} ${b} ${c}\n`;
    }

    return ply;
  }

  /**
   * Export au format STL (Stereolithography)
   */
  exportSTL() {
    let stl = `solid model_3d\n`;

    // Calculer les normales et exporter les triangles
    for (let i = 0; i < this.faces.length; i += 3) {
      const i1 = this.faces[i] * 3;
      const i2 = this.faces[i + 1] * 3;
      const i3 = this.faces[i + 2] * 3;

      // Vertices du triangle
      const v1 = [this.vertices[i1], this.vertices[i1 + 1], this.vertices[i1 + 2]];
      const v2 = [this.vertices[i2], this.vertices[i2 + 1], this.vertices[i2 + 2]];
      const v3 = [this.vertices[i3], this.vertices[i3 + 1], this.vertices[i3 + 2]];

      // Calculer la normale
      const normal = this.calculateNormal(v1, v2, v3);

      stl += `  facet normal ${normal[0].toFixed(6)} ${normal[1].toFixed(6)} ${normal[2].toFixed(6)}\n`;
      stl += `    outer loop\n`;
      stl += `      vertex ${v1[0].toFixed(6)} ${v1[1].toFixed(6)} ${v1[2].toFixed(6)}\n`;
      stl += `      vertex ${v2[0].toFixed(6)} ${v2[1].toFixed(6)} ${v2[2].toFixed(6)}\n`;
      stl += `      vertex ${v3[0].toFixed(6)} ${v3[1].toFixed(6)} ${v3[2].toFixed(6)}\n`;
      stl += `    endloop\n`;
      stl += `  endfacet\n`;
    }

    stl += `endsolid model_3d\n`;
    return stl;
  }

  /**
   * Export au format JSON complet
   */
  exportJSON() {
    const exportData = {
      metadata: {
        version: "1.0",
        type: "3D Body Model",
        generator: "3D Body Measurement App",
        date: new Date().toISOString(),
        model_type: this.modelData.model_type,
        has_real_model: this.modelData.model_info?.has_real_model || false
      },
      measurements: this.measurements,
      smpl_parameters: {
        betas: this.betas,
        gender: this.measurements.gender || 'neutral'
      },
      geometry: {
        vertices: this.vertices,
        faces: this.faces,
        vertex_count: this.vertices.length / 3,
        face_count: this.faces.length / 3
      },
      analysis: this.modelData.measurement_analysis || {},
      model_info: this.modelData.model_info || {}
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Export des mesures seules au format CSV
   */
  exportMeasurementsCSV() {
    let csv = 'Mesure,Valeur,Unité\n';
    
    Object.entries(this.measurements).forEach(([key, value]) => {
      const unit = this.getUnitForMeasurement(key);
      csv += `${this.formatMeasurementName(key)},${value},${unit}\n`;
    });

    // Ajouter les paramètres SMPL
    csv += '\nParamètre SMPL,Valeur,Description\n';
    this.betas.forEach((beta, index) => {
      csv += `Beta ${index},${beta.toFixed(6)},${this.getBetaDescription(index)}\n`;
    });

    return csv;
  }

  /**
   * Calculer la normale d'un triangle
   */
  calculateNormal(v1, v2, v3) {
    const u = [v2[0] - v1[0], v2[1] - v1[1], v2[2] - v1[2]];
    const v = [v3[0] - v1[0], v3[1] - v1[1], v3[2] - v1[2]];

    const normal = [
      u[1] * v[2] - u[2] * v[1],
      u[2] * v[0] - u[0] * v[2],
      u[0] * v[1] - u[1] * v[0]
    ];

    // Normaliser
    const length = Math.sqrt(normal[0] * normal[0] + normal[1] * normal[1] + normal[2] * normal[2]);
    if (length > 0) {
      normal[0] /= length;
      normal[1] /= length;
      normal[2] /= length;
    }

    return normal;
  }

  /**
   * Obtenir l'unité pour une mesure
   */
  getUnitForMeasurement(key) {
    if (key === 'weight') return 'kg';
    if (key === 'age') return 'années';
    if (key === 'gender') return '';
    if (key.includes('Name')) return '';
    return 'cm';
  }

  /**
   * Formater le nom d'une mesure
   */
  formatMeasurementName(key) {
    const names = {
      firstName: 'Prénom',
      lastName: 'Nom',
      gender: 'Genre',
      age: 'Âge',
      height: 'Taille',
      weight: 'Poids',
      chest: 'Tour de poitrine',
      waist: 'Tour de taille',
      hips: 'Tour de hanches',
      shoulderWidth: 'Largeur d\'épaules',
      neckCircumference: 'Tour de cou',
      armLength: 'Longueur du bras',
      armSpan: 'Envergure',
      wristCircumference: 'Tour de poignet',
      inseam: 'Entrejambe',
      thighCircumference: 'Tour de cuisse',
      calfCircumference: 'Tour de mollet',
      ankleCircumference: 'Tour de cheville',
      torsoLength: 'Longueur du torse',
      backWidth: 'Largeur du dos',
      headCircumference: 'Tour de tête',
      footLength: 'Longueur du pied',
      footWidth: 'Largeur du pied'
    };
    return names[key] || key;
  }

  /**
   * Description des paramètres beta SMPL
   */
  getBetaDescription(index) {
    const descriptions = [
      'Taille générale',
      'Largeur du corps',
      'Tour de taille',
      'Tour de hanches',
      'Longueur du torse',
      'Longueur des bras',
      'Ratio épaules/hanches',
      'Circonférence des cuisses',
      'Longueur des jambes',
      'Circonférence du cou'
    ];
    return descriptions[index] || `Paramètre ${index}`;
  }

  /**
   * Télécharger un fichier
   */
  static downloadFile(content, filename, mimeType = 'text/plain') {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Export avec sélection automatique du nom de fichier
   */
  export(format) {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const gender = this.measurements.gender || 'neutral';
    const height = this.measurements.height || 'unknown';
    
    let content, filename, mimeType;

    switch (format.toLowerCase()) {
      case 'obj':
        content = this.exportOBJ();
        filename = `model_3d_${gender}_${height}cm_${timestamp}.obj`;
        mimeType = 'text/plain';
        break;
      
      case 'ply':
        content = this.exportPLY();
        filename = `model_3d_${gender}_${height}cm_${timestamp}.ply`;
        mimeType = 'text/plain';
        break;
      
      case 'stl':
        content = this.exportSTL();
        filename = `model_3d_${gender}_${height}cm_${timestamp}.stl`;
        mimeType = 'text/plain';
        break;
      
      case 'json':
        content = this.exportJSON();
        filename = `model_3d_${gender}_${height}cm_${timestamp}.json`;
        mimeType = 'application/json';
        break;
      
      case 'csv':
        content = this.exportMeasurementsCSV();
        filename = `measurements_${gender}_${height}cm_${timestamp}.csv`;
        mimeType = 'text/csv';
        break;
      
      default:
        throw new Error(`Format d'export non supporté: ${format}`);
    }

    ModelExporter.downloadFile(content, filename, mimeType);
    return { filename, size: content.length };
  }
}

export default ModelExporter;
