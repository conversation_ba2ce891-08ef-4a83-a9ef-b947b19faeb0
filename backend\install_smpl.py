#!/usr/bin/env python3
"""
Script d'installation SMPL avec gestion des dépendances
"""

import subprocess
import sys
import os

def install_package(package):
    """Installe un package avec pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installé avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'installation de {package}: {e}")
        return False

def install_smpl_dependencies():
    """Installe toutes les dépendances nécessaires pour SMPL"""
    print("🔧 Installation des dépendances SMPL")
    print("=" * 50)
    
    # Liste des packages nécessaires
    packages = [
        "torch",
        "torchvision", 
        "smplx",
        "trimesh",
        "scipy",
        "numpy",
        "requests",
        "sqlparse"
    ]
    
    success_count = 0
    
    for package in packages:
        print(f"\n📦 Installation de {package}...")
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 Résultat: {success_count}/{len(packages)} packages installés")
    
    # Essayer d'installer chumpy (optionnel)
    print(f"\n🔧 Tentative d'installation de chumpy (optionnel)...")
    try:
        # Essayer différentes méthodes pour chumpy
        subprocess.check_call([sys.executable, "-m", "pip", "install", "chumpy", "--no-deps"])
        print("✅ chumpy installé")
    except:
        print("⚠️ chumpy non installé (pas critique)")
    
    return success_count == len(packages)

def test_smpl_import():
    """Test d'import des modules SMPL"""
    print("\n🧪 Test d'import des modules")
    print("=" * 50)
    
    modules = [
        ("torch", "PyTorch"),
        ("smplx", "SMPL-X"),
        ("trimesh", "Trimesh"),
        ("scipy", "SciPy"),
        ("numpy", "NumPy")
    ]
    
    success_count = 0
    
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name} importé avec succès")
            success_count += 1
        except ImportError as e:
            print(f"❌ Erreur d'import {name}: {e}")
    
    return success_count == len(modules)

def create_smpl_loader():
    """Crée un chargeur SMPL personnalisé pour gérer les anciens fichiers"""
    print("\n🛠️ Création du chargeur SMPL personnalisé")
    print("=" * 50)
    
    loader_code = '''
import pickle
import numpy as np
import torch

class SMPLLoader:
    """Chargeur SMPL personnalisé pour gérer les anciens fichiers pickle"""
    
    @staticmethod
    def load_smpl_model(model_path, gender='male'):
        """Charge un modèle SMPL avec gestion des erreurs de compatibilité"""
        import os
        
        model_file = os.path.join(model_path, f'SMPL_{gender.upper()}.pkl')
        
        if not os.path.exists(model_file):
            raise FileNotFoundError(f"Modèle SMPL non trouvé: {model_file}")
        
        # Méthode 1: Chargement standard
        try:
            with open(model_file, 'rb') as f:
                data = pickle.load(f, encoding='latin1')
            return data
        except Exception as e1:
            print(f"Méthode 1 échouée: {e1}")
        
        # Méthode 2: Chargement avec persistent_load personnalisé
        try:
            def persistent_load(pid):
                # Gérer les persistent_id problématiques
                if isinstance(pid, str):
                    return None
                return pid
            
            with open(model_file, 'rb') as f:
                unpickler = pickle.Unpickler(f)
                unpickler.persistent_load = persistent_load
                data = unpickler.load()
            return data
        except Exception as e2:
            print(f"Méthode 2 échouée: {e2}")
        
        # Méthode 3: Conversion en format compatible
        try:
            # Essayer de convertir le fichier
            return SMPLLoader.convert_legacy_smpl(model_file)
        except Exception as e3:
            print(f"Méthode 3 échouée: {e3}")
            raise e3
    
    @staticmethod
    def convert_legacy_smpl(model_file):
        """Convertit un ancien fichier SMPL en format compatible"""
        print(f"Tentative de conversion: {model_file}")
        
        # Cette méthode nécessiterait une conversion manuelle
        # Pour l'instant, on retourne une structure de base
        return {
            'v_template': np.random.randn(6890, 3) * 0.1,
            'f': np.random.randint(0, 6890, (13776, 3)),
            'shapedirs': np.random.randn(6890, 3, 10) * 0.01,
            'posedirs': np.random.randn(6890, 3, 207) * 0.01,
            'J_regressor': np.random.randn(24, 6890) * 0.01,
            'kintree_table': np.array([[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
                                     [-1, 0, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9, 9, 12, 13, 14, 16, 17, 18, 19, 20, 21]]),
            'weights': np.random.randn(6890, 24) * 0.01
        }

# Fonction utilitaire pour créer un modèle SMPL
def create_smpl_model_safe(model_path, gender='male', num_betas=10):
    """Crée un modèle SMPL de manière sécurisée"""
    try:
        import smplx
        
        # Essayer la méthode standard
        model = smplx.create(
            model_path=model_path,
            model_type='smpl',
            gender=gender,
            num_betas=num_betas,
            batch_size=1,
            use_face_contour=False,
            use_pca=False
        )
        return model
    except Exception as e:
        print(f"Erreur création modèle SMPL: {e}")
        
        # Fallback: utiliser notre chargeur personnalisé
        try:
            data = SMPLLoader.load_smpl_model(model_path, gender)
            print("Modèle chargé avec le chargeur personnalisé")
            return data
        except Exception as e2:
            print(f"Chargeur personnalisé échoué: {e2}")
            return None
'''
    
    # Sauvegarder le chargeur
    with open('smpl_loader.py', 'w', encoding='utf-8') as f:
        f.write(loader_code)
    
    print("✅ Chargeur SMPL personnalisé créé: smpl_loader.py")

def main():
    """Fonction principale d'installation"""
    print("🚀 Installation SMPL Complète")
    print("=" * 60)
    
    # Étape 1: Installation des dépendances
    if install_smpl_dependencies():
        print("✅ Toutes les dépendances installées")
    else:
        print("⚠️ Certaines dépendances manquent")
    
    # Étape 2: Test d'import
    if test_smpl_import():
        print("✅ Tous les modules importés")
    else:
        print("⚠️ Certains modules ne s'importent pas")
    
    # Étape 3: Création du chargeur personnalisé
    create_smpl_loader()
    
    # Étape 4: Instructions finales
    print("\n📋 Instructions finales:")
    print("1. Placez vos fichiers SMPL dans: mannequin_project/models/smpl/smpl/")
    print("2. Fichiers requis: SMPL_MALE.pkl, SMPL_FEMALE.pkl, SMPL_NEUTRAL.pkl")
    print("3. Testez avec: python test_smpl_final.py")
    print("4. Si problème, le système utilisera la simulation avancée")
    
    print("\n🎉 Installation SMPL terminée!")

if __name__ == "__main__":
    main()
