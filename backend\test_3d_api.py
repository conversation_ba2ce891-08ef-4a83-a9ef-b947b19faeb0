#!/usr/bin/env python3
"""
Script de test pour l'API de génération de modèles 3D
"""

import requests
import json

def test_3d_body_api():
    """Test de l'API de génération de modèles 3D avec des mesures complètes"""
    
    # URL de l'API
    url = "http://localhost:8000/3d-body/"
    
    # Données de test - mesures d'une personne de taille moyenne
    test_data = {
        "genre": "male",
        "taille": 175.0,
        "weight": 75.0,
        "poitrine": 95.0,
        "tour_taille": 80.0,
        "hanches": 90.0,
        "additional_measurements": {
            "firstName": "Test",
            "lastName": "User",
            "age": "30",
            "shoulderWidth": "45.0",
            "neckCircumference": "38.0",
            "armLength": "65.0",
            "armSpan": "175.0",
            "wristCircumference": "16.0",
            "inseam": "82.0",
            "thighCircumference": "55.0",
            "calfCircumference": "35.0",
            "ankleCircumference": "22.0",
            "torsoLength": "60.0",
            "backWidth": "40.0",
            "headCircumference": "56.0",
            "footLength": "26.0",
            "footWidth": "10.0"
        }
    }
    
    print("🧪 Test de l'API de génération de modèles 3D")
    print("=" * 50)
    print(f"URL: {url}")
    print(f"Données: {json.dumps(test_data, indent=2)}")
    print()
    
    try:
        # Envoyer la requête
        print("📤 Envoi de la requête...")
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📥 Réponse reçue - Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Succès!")
            print("-" * 30)
            print(f"🎯 Message: {result.get('message', 'N/A')}")
            print(f"🔧 Type de modèle: {result.get('model_type', 'N/A')}")
            print(f"🔢 Vertices: {result.get('vertices_count', 'N/A'):,}")
            print(f"🔺 Faces: {result.get('faces_count', 'N/A'):,}")
            print(f"👤 Genre: {result.get('gender', 'N/A')}")
            
            # Afficher les paramètres SMPL betas
            if 'betas' in result:
                betas = result['betas']
                print(f"🧬 Paramètres SMPL betas:")
                for i, beta in enumerate(betas):
                    print(f"   Beta {i}: {beta:.3f}")
            
            # Afficher l'analyse morphologique
            if 'measurement_analysis' in result:
                analysis = result['measurement_analysis']
                print(f"📊 Analyse morphologique:")
                print(f"   - Catégorie taille: {analysis.get('height_category', 'N/A')}")
                print(f"   - Type de corps: {analysis.get('body_type', 'N/A')}")
                print(f"   - IMC: {analysis.get('bmi', 'N/A')}")
            
            # Informations sur le modèle
            if 'model_info' in result:
                model_info = result['model_info']
                print(f"ℹ️ Informations du modèle:")
                print(f"   - Version SMPL: {model_info.get('smpl_version', 'N/A')}")
                print(f"   - Modèle réel: {'Oui' if model_info.get('has_real_model') else 'Non (simulation)'}")
                if 'note' in model_info:
                    print(f"   - Note: {model_info['note']}")
            
            return True
            
        else:
            print("❌ Erreur!")
            print(f"Status: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Erreur: {error_data.get('error', 'Erreur inconnue')}")
            except:
                print(f"Réponse: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Erreur de connexion!")
        print("Assurez-vous que le serveur Django est démarré sur le port 8000")
        print("Commande: python manage.py runserver")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ Timeout!")
        print("La requête a pris trop de temps")
        return False
        
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_different_body_types():
    """Test avec différents types de morphologies"""
    
    test_cases = [
        {
            "name": "Femme petite",
            "data": {
                "genre": "female",
                "taille": 160.0,
                "weight": 55.0,
                "poitrine": 85.0,
                "tour_taille": 65.0,
                "hanches": 90.0,
                "additional_measurements": {
                    "shoulderWidth": "40.0",
                    "armLength": "55.0",
                    "inseam": "75.0"
                }
            }
        },
        {
            "name": "Homme grand",
            "data": {
                "genre": "male",
                "taille": 190.0,
                "weight": 85.0,
                "poitrine": 105.0,
                "tour_taille": 90.0,
                "hanches": 95.0,
                "additional_measurements": {
                    "shoulderWidth": "50.0",
                    "armLength": "70.0",
                    "inseam": "90.0"
                }
            }
        }
    ]
    
    print("\n🧪 Test de différents types de corps")
    print("=" * 50)
    
    for test_case in test_cases:
        print(f"\n📋 Test: {test_case['name']}")
        print("-" * 20)
        
        try:
            response = requests.post("http://localhost:8000/3d-body/", 
                                   json=test_case['data'], timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {test_case['name']}: Succès")
                if 'measurement_analysis' in result:
                    analysis = result['measurement_analysis']
                    print(f"   Type: {analysis.get('body_type', 'N/A')}")
                    print(f"   IMC: {analysis.get('bmi', 'N/A')}")
            else:
                print(f"❌ {test_case['name']}: Erreur {response.status_code}")
                
        except Exception as e:
            print(f"❌ {test_case['name']}: Exception {e}")

if __name__ == "__main__":
    print("🚀 Démarrage des tests de l'API 3D Body")
    print()
    
    # Test principal
    success = test_3d_body_api()
    
    if success:
        # Tests supplémentaires
        test_different_body_types()
        
        print("\n🎉 Tests terminés!")
        print("💡 Vous pouvez maintenant tester le formulaire sur http://localhost:5174/3d-body")
    else:
        print("\n💡 Conseils de dépannage:")
        print("1. Vérifiez que le serveur Django est démarré: python manage.py runserver")
        print("2. Vérifiez que le serveur frontend est démarré: npm run dev")
        print("3. Vérifiez les logs du serveur Django pour plus de détails")
