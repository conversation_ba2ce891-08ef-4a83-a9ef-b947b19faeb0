# Intégration SMPL avec le Formulaire de Mesures 3D

## Vue d'ensemble

Ce document explique comment le formulaire de mesures corporelles est relié au modèle SMPL (Skinned Multi-Person Linear model) pour générer des avatars 3D personnalisés.

## Architecture

### 1. Formulaire Frontend (`/3d-body`)
Le formulaire collecte les mesures suivantes :

#### Mesures de Base
- **Taille** (height) - en cm
- **Poids** (weight) - en kg
- **Tour de poitrine** (chest)
- **Tour de taille** (waist)
- **Tour de hanches** (hips)

#### Mesures Détaillées
- **Épaules** : largeur d'épaules
- **Bras** : longueur, envergure, tour de poignet
- **Jambes** : entrejambe, tour de cuisse, mollet, cheville
- **Torse** : longueur du torse, largeur du dos
- **Tête et pieds** : circonférence tête, longueur/largeur pieds
- **Cou** : circonférence du cou

### 2. Conversion en Paramètres SMPL

#### Paramètres Beta SMPL
Le modèle SMPL utilise 10 paramètres "beta" pour contrôler la forme du corps :

```python
betas[0] = Taille générale (height + weight)
betas[1] = Largeur du corps (chest + shoulders)
betas[2] = Tour de taille
betas[3] = Tour de hanches
betas[4] = Longueur du torse
betas[5] = Longueur des bras
betas[6] = Ratio épaules/hanches
betas[7] = Circonférence des cuisses
betas[8] = Longueur des jambes
betas[9] = Circonférence du cou
```

#### Fonction de Conversion
```python
def measurements_to_smpl_betas(measurements):
    # Valeurs de référence pour un corps "moyen"
    reference = {
        'height': 170.0, 'chest': 90.0, 'waist': 75.0,
        'hips': 95.0, 'shoulder_width': 45.0, ...
    }
    
    # Calcul des différences normalisées
    betas[0] = (height_diff + weight_diff) / 2.0
    betas[1] = (chest_diff + shoulder_diff) / 2.0
    # ... etc
    
    return np.clip(betas, -3.0, 3.0)  # Limiter les déformations
```

### 3. Génération du Modèle 3D

#### Avec SMPL Réel
```python
# Créer le modèle SMPL
model = smplx.create(
    model_path='models/smpl',
    model_type='smpl',
    gender=gender,
    num_betas=10
)

# Générer les vertices
betas_tensor = torch.tensor(betas).unsqueeze(0)
output = model(betas=betas_tensor)
vertices = output.vertices.detach().numpy()[0]
```

#### Mode Simulation (Fallback)
Si les modèles SMPL ne sont pas disponibles :
```python
# Génération simulée basée sur les mesures
mock_vertices = np.random.randn(6890, 3) * 0.05
mock_vertices[:, 1] *= height_scale  # Ajuster hauteur
mock_vertices[:, 0] *= width_scale   # Ajuster largeur
```

## API Endpoints

### POST `/3d-body/`
**Entrée :**
```json
{
  "genre": "male|female|neutral",
  "taille": 175.0,
  "weight": 70.0,
  "poitrine": 95.0,
  "tour_taille": 80.0,
  "hanches": 100.0,
  "additional_measurements": {
    "shoulderWidth": 45.0,
    "armLength": 65.0,
    "inseam": 85.0,
    ...
  }
}
```

**Sortie :**
```json
{
  "success": true,
  "message": "Modèle 3D généré avec succès",
  "model_type": "SMPL|SIMULATION_ENHANCED",
  "vertices_count": 6890,
  "faces_count": 13776,
  "gender": "male",
  "measurements": {...},
  "betas": [0.2, -0.1, 0.5, ...],
  "measurement_analysis": {
    "height_category": "tall",
    "body_type": "sablier",
    "bmi": 22.9
  },
  "model_info": {
    "has_real_model": true,
    "smpl_version": "SMPL"
  }
}
```

## Analyse Morphologique

Le système analyse automatiquement :

### Types de Morphologie
- **Sablier** : taille marquée, épaules et hanches équilibrées
- **Triangle inverse** : épaules > hanches
- **Poire** : hanches > épaules
- **Rectangle** : proportions similaires
- **Ovale** : autres cas

### Calculs
```python
def analyze_body_type(measurements):
    waist_hip_ratio = waist / hips
    chest_waist_ratio = chest / waist
    
    if waist_hip_ratio < 0.8 and chest_waist_ratio > 1.2:
        return "sablier"
    # ... autres conditions
```

## Installation SMPL

### Prérequis
1. Télécharger les modèles SMPL depuis https://smpl.is.tue.mpg.de/
2. Installer smplx : `pip install smplx`
3. Placer les modèles dans `backend/models/smpl/`

### Structure des Fichiers
```
backend/models/smpl/
├── SMPL_FEMALE.pkl
├── SMPL_MALE.pkl
└── SMPL_NEUTRAL.pkl
```

## Utilisation

1. **Remplir le formulaire** sur `/3d-body`
2. **Soumission** → conversion en paramètres SMPL
3. **Génération** → modèle 3D personnalisé
4. **Réponse** → informations détaillées + analyse morphologique

## Développement

### Tests
```bash
# Tester l'API
curl -X POST http://localhost:8000/3d-body/ \
  -H "Content-Type: application/json" \
  -d '{"genre":"male","taille":175,"poitrine":95,...}'
```

### Logs
Les logs détaillés sont disponibles dans la console Django pour le debugging.

### Améliorations Futures
- Visualisation 3D en temps réel
- Export des modèles (OBJ, PLY)
- Ajustements fins des paramètres
- Intégration avec des outils de mode/design
