
# Instructions pour intégrer SMPL dans generateur/views.py

1. Ajouter en haut du fichier:
   from smpl_loader import create_smpl_model_safe
   import torch

2. Remplacer la fonction create_smpl_model par:
   
   def create_smpl_model(gender):
       """Crée un modèle SMPL réel"""
       try:
           model_path = os.path.join(settings.BASE_DIR, 'mannequin_project', 'models', 'smpl')
           model = create_smpl_model_safe(model_path, gender, 10)
           
           if model is not None:
               logger.info(f"Modèle SMPL {gender} chargé avec succès")
               return model
           else:
               logger.warning(f"Impossible de charger le modèle SMPL {gender}")
               return None
               
       except Exception as e:
           logger.error(f"Erreur lors du chargement SMPL {gender}: {e}")
           return None

3. Dans la fonction generate_3d_body, remplacer:
   
   # Utiliser notre simulation humanoïde avancée
   return generate_advanced_humanoid_model(all_measurements, betas, gender)
   
   Par:
   
   # Essayer d'utiliser SMPL réel
   smpl_result = generate_smpl_model_real(all_measurements, betas, gender)
   if smpl_result:
       return JsonResponse(smpl_result)
   else:
       # Fallback vers simulation
       return generate_advanced_humanoid_model(all_measurements, betas, gender)

4. Ajouter la fonction generate_smpl_model_real (voir smpl_integration.py)
