{"name": "mosaic-react", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-popover": "^1.1.2", "@tailwindcss/forms": "^0.5.7", "chart.js": "^4.4.1", "chartjs-adapter-moment": "^1.0.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "moment": "^2.29.4", "react": "^19.0.0", "react-day-picker": "^9.4.1", "react-dom": "^19.0.0", "react-router-dom": "^7.0.2", "react-transition-group": "^4.4.5", "tailwind-merge": "^2.5.5", "three": "^0.179.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "@vitejs/plugin-react": "^4.2.1", "postcss": "^8.4.32", "tailwindcss": "^4.0.0", "vite": "^6.0.3"}}