#!/usr/bin/env python3
"""
Test direct des modèles SMPL
"""

import os
import sys
import torch
import numpy as np

def test_smpl_direct():
    """Test direct de SMPL"""
    print("🧪 Test direct des modèles SMPL")
    print("=" * 50)
    
    try:
        import smplx
        print("✅ smplx importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur d'import smplx: {e}")
        return False
    
    # Chemin vers les modèles
    model_path = "mannequin_project/models/smpl"
    
    if not os.path.exists(model_path):
        print(f"❌ Répertoire non trouvé: {model_path}")
        return False
    
    print(f"✅ Répertoire trouvé: {model_path}")
    
    # Lister les fichiers
    files = os.listdir(model_path)
    print(f"📁 Fichiers disponibles: {files}")
    
    # Tester le modèle masculin
    try:
        print("\n🚹 Test du modèle masculin...")
        
        model = smplx.create(
            model_path=model_path,
            model_type='smpl',
            gender='male',
            num_betas=10,
            batch_size=1,
            use_face_contour=False,
            use_pca=False
        )
        
        print(f"✅ Modèle masculin chargé")
        print(f"   - Vertices: {model.get_num_verts():,}")
        print(f"   - Faces: {model.faces.shape[0]:,}")
        
        # Test de génération
        betas = torch.randn(1, 10) * 0.3  # Variations modérées
        output = model(betas=betas)
        vertices = output.vertices.detach().numpy()[0]
        
        print(f"   - Génération réussie: {vertices.shape}")
        print(f"   - Bounding box: X[{vertices[:, 0].min():.3f}, {vertices[:, 0].max():.3f}]")
        print(f"   - Bounding box: Y[{vertices[:, 1].min():.3f}, {vertices[:, 1].max():.3f}]")
        print(f"   - Bounding box: Z[{vertices[:, 2].min():.3f}, {vertices[:, 2].max():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur avec le modèle masculin: {e}")
        print(f"   Type d'erreur: {type(e).__name__}")
        return False

def test_measurements_conversion():
    """Test de conversion des mesures"""
    print("\n🧬 Test de conversion des mesures")
    print("=" * 50)
    
    try:
        # Ajouter le répertoire generateur au path
        sys.path.append('.')
        from generateur.views import measurements_to_smpl_betas
        
        # Mesures de test
        test_measurements = {
            'height': 175.0,
            'weight': 70.0,
            'chest': 95.0,
            'waist': 80.0,
            'hips': 90.0,
            'shoulder_width': 45.0,
            'arm_length': 65.0,
            'inseam': 82.0
        }
        
        print(f"📏 Mesures de test: {test_measurements}")
        
        betas = measurements_to_smpl_betas(test_measurements)
        print(f"🧬 Betas calculés: {betas}")
        print(f"   - Type: {type(betas)}")
        print(f"   - Shape: {betas.shape}")
        print(f"   - Min/Max: [{betas.min():.3f}, {betas.max():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la conversion: {e}")
        return False

def test_full_pipeline():
    """Test du pipeline complet"""
    print("\n🔄 Test du pipeline complet")
    print("=" * 50)
    
    try:
        import smplx
        sys.path.append('.')
        from generateur.views import measurements_to_smpl_betas
        
        # Mesures de test
        measurements = {
            'height': 180.0,
            'weight': 75.0,
            'chest': 100.0,
            'waist': 85.0,
            'hips': 95.0,
            'shoulder_width': 48.0
        }
        
        print(f"📏 Mesures: {measurements}")
        
        # Conversion en betas
        betas = measurements_to_smpl_betas(measurements)
        print(f"🧬 Betas: {betas}")
        
        # Création du modèle
        model = smplx.create(
            model_path="mannequin_project/models/smpl",
            model_type='smpl',
            gender='male',
            num_betas=10,
            batch_size=1
        )
        
        # Génération
        betas_tensor = torch.tensor(betas, dtype=torch.float32).unsqueeze(0)
        output = model(betas=betas_tensor)
        vertices = output.vertices.detach().numpy()[0]
        faces = model.faces
        
        print(f"✅ Pipeline complet réussi!")
        print(f"   - Vertices générés: {vertices.shape}")
        print(f"   - Faces: {faces.shape}")
        print(f"   - Taille du modèle: {vertices.nbytes / 1024:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans le pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Tests directs SMPL")
    print("=" * 60)
    
    success_count = 0
    
    if test_smpl_direct():
        success_count += 1
    
    if test_measurements_conversion():
        success_count += 1
    
    if test_full_pipeline():
        success_count += 1
    
    print(f"\n📊 Résultats: {success_count}/3 tests réussis")
    
    if success_count == 3:
        print("🎉 SMPL est complètement fonctionnel!")
        print("💡 Vous pouvez maintenant utiliser les vrais modèles SMPL")
    else:
        print("⚠️ Certains tests ont échoué")
        print("💡 Vérifiez l'installation et les chemins")
