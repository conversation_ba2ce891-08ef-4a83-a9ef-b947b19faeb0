import torch
import os
import json
import numpy as np
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import logging
from django.conf import settings
try:
    import smplx
except ImportError:
    smplx = None
    print("SMPLX not installed. Using mock implementation.")

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def measurements_to_smpl_betas(measurements):
    """
    Convertit les mesures corporelles en paramètres betas SMPL.
    Les betas contrôlent la forme du corps dans SMPL.
    """
    # Valeurs de référence pour un corps "moyen"
    reference_measurements = {
        'height': 170.0,
        'chest': 90.0,
        'waist': 75.0,
        'hips': 95.0,
        'shoulder_width': 45.0,
        'neck_circumference': 38.0,
        'arm_length': 60.0,
        'arm_span': 170.0,
        'torso_length': 60.0,
        'inseam': 80.0,
        'thigh_circumference': 55.0,
        'weight': 70.0
    }

    # Initialiser les betas (10 paramètres principaux pour SMPL)
    betas = np.zeros(10)

    # Beta 0: <PERSON><PERSON> (height, weight)
    height_diff = (measurements.get('height', 170) - reference_measurements['height']) / 20.0
    weight_diff = (measurements.get('weight', 70) - reference_measurements['weight']) / 20.0
    betas[0] = (height_diff + weight_diff) / 2.0

    # Beta 1: Largeur du corps (chest, shoulders)
    chest_diff = (measurements.get('chest', 90) - reference_measurements['chest']) / 15.0
    shoulder_diff = (measurements.get('shoulder_width', 45) - reference_measurements['shoulder_width']) / 10.0
    betas[1] = (chest_diff + shoulder_diff) / 2.0

    # Beta 2: Tour de taille
    waist_diff = (measurements.get('waist', 75) - reference_measurements['waist']) / 12.0
    betas[2] = waist_diff

    # Beta 3: Hanches
    hips_diff = (measurements.get('hips', 95) - reference_measurements['hips']) / 15.0
    betas[3] = hips_diff

    # Beta 4: Longueur du torse
    torso_diff = (measurements.get('torso_length', 60) - reference_measurements['torso_length']) / 10.0
    betas[4] = torso_diff

    # Beta 5: Longueur des bras
    arm_diff = (measurements.get('arm_length', 60) - reference_measurements['arm_length']) / 10.0
    betas[5] = arm_diff

    # Beta 6: Largeur des épaules vs hanches (ratio)
    shoulder_hip_ratio = measurements.get('shoulder_width', 45) / measurements.get('hips', 95)
    reference_ratio = reference_measurements['shoulder_width'] / reference_measurements['hips']
    betas[6] = (shoulder_hip_ratio - reference_ratio) * 5.0

    # Beta 7: Circonférence des cuisses
    thigh_diff = (measurements.get('thigh_circumference', 55) - reference_measurements['thigh_circumference']) / 10.0
    betas[7] = thigh_diff

    # Beta 8: Longueur des jambes (inseam)
    inseam_diff = (measurements.get('inseam', 80) - reference_measurements['inseam']) / 15.0
    betas[8] = inseam_diff

    # Beta 9: Circonférence du cou
    neck_diff = (measurements.get('neck_circumference', 38) - reference_measurements['neck_circumference']) / 5.0
    betas[9] = neck_diff

    # Limiter les valeurs des betas pour éviter des déformations extrêmes
    betas = np.clip(betas, -3.0, 3.0)

    return betas

def create_smpl_model(gender='neutral'):
    """
    Crée un modèle SMPL/SMPLX selon le genre spécifié.
    """
    if smplx is None:
        logger.warning("SMPLX non disponible, utilisation de la simulation")
        return None

    try:
        # Chemin vers les modèles SMPL (à adapter selon votre installation)
        model_path = os.path.join(settings.BASE_DIR, 'models', 'smpl')

        if not os.path.exists(model_path):
            logger.warning(f"Modèles SMPL non trouvés dans {model_path}")
            return None

        # Créer le modèle SMPL
        model = smplx.create(
            model_path=model_path,
            model_type='smpl',
            gender=gender,
            use_face_contour=False,
            use_pca=False,
            num_betas=10,
            batch_size=1
        )

        return model

    except Exception as e:
        logger.error(f"Erreur lors de la création du modèle SMPL : {e}")
        return None

@csrf_exempt
def generate_3d_body_model(request):
    """Génère un modèle 3D basé sur les mesures corporelles en utilisant SMPL"""
    if request.method == 'POST':
        try:
            logger.debug("Requête reçue : %s", request.body)
            data = json.loads(request.body)

            # Extraire les mesures de base
            basic_measurements = {
                'height': float(data.get("taille", 170)),
                'weight': float(data.get("weight", 70)),
                'chest': float(data.get("poitrine", 90)),
                'waist': float(data.get("tour_taille", 75)),
                'hips': float(data.get("hanches", 95)),
            }

            gender = data.get("genre", "neutral").lower()

            # Extraire les mesures additionnelles du formulaire
            additional_measurements = data.get("additional_measurements", {})

            # Combiner toutes les mesures
            all_measurements = {
                **basic_measurements,
                'shoulder_width': float(additional_measurements.get('shoulderWidth', 45)),
                'neck_circumference': float(additional_measurements.get('neckCircumference', 38)),
                'arm_length': float(additional_measurements.get('armLength', 60)),
                'arm_span': float(additional_measurements.get('armSpan', 170)),
                'torso_length': float(additional_measurements.get('torsoLength', 60)),
                'inseam': float(additional_measurements.get('inseam', 80)),
                'thigh_circumference': float(additional_measurements.get('thighCircumference', 55)),
                'calf_circumference': float(additional_measurements.get('calfCircumference', 35)),
                'ankle_circumference': float(additional_measurements.get('ankleCircumference', 22)),
                'back_width': float(additional_measurements.get('backWidth', 40)),
                'head_circumference': float(additional_measurements.get('headCircumference', 56)),
                'foot_length': float(additional_measurements.get('footLength', 26)),
                'foot_width': float(additional_measurements.get('footWidth', 10)),
                'wrist_circumference': float(additional_measurements.get('wristCircumference', 16)),
            }

            logger.debug("Mesures complètes reçues : %s", all_measurements)

            # Convertir les mesures en paramètres SMPL betas
            betas = measurements_to_smpl_betas(all_measurements)
            logger.debug("Betas SMPL calculés : %s", betas.tolist())

            # Essayer de créer un vrai modèle SMPL
            smpl_model = create_smpl_model(gender)

            if smpl_model is not None:
                # Utiliser le vrai modèle SMPL
                try:
                    betas_tensor = torch.tensor(betas, dtype=torch.float32).unsqueeze(0)

                    # Générer le modèle 3D
                    output = smpl_model(betas=betas_tensor)
                    vertices = output.vertices.detach().numpy()[0]
                    faces = smpl_model.faces

                    logger.info("Modèle SMPL généré avec succès : %s vertices, %s faces",
                               vertices.shape[0], faces.shape[0])

                    return JsonResponse({
                        "success": True,
                        "message": "Modèle 3D SMPL généré avec succès",
                        "model_type": "SMPL",
                        "vertices_count": int(vertices.shape[0]),
                        "faces_count": int(faces.shape[0]),
                        "gender": gender,
                        "measurements": all_measurements,
                        "betas": betas.tolist(),
                        "vertices": vertices.tolist(),  # Attention: peut être volumineux
                        "faces": faces.tolist(),
                        "model_info": {
                            "smpl_version": "SMPL",
                            "num_betas": len(betas),
                            "has_real_model": True
                        }
                    })

                except Exception as e:
                    logger.error("Erreur lors de l'utilisation du modèle SMPL : %s", str(e))
                    # Fallback vers la simulation
                    pass

            # Fallback: Simulation améliorée si SMPL n'est pas disponible
            return generate_mock_model_with_measurements(all_measurements, betas, gender)

        except Exception as e:
            logger.error("Erreur lors de la génération du modèle 3D : %s", str(e), exc_info=True)
            return JsonResponse({"error": f"Erreur interne : {str(e)}"}, status=500)

    return JsonResponse({"error": "Méthode non autorisée"}, status=405)

def generate_mock_model_with_measurements(measurements, betas, gender):
    """Génère un modèle simulé amélioré basé sur les mesures"""
    # Simulation améliorée avec les vraies mesures
    vertices_count = 6890  # Standard SMPL
    faces_count = 13776

    # Générer des vertices plus réalistes basés sur les mesures
    mock_vertices = np.random.randn(vertices_count, 3) * 0.05

    # Ajustements basés sur les mesures réelles
    height_scale = measurements['height'] / 170.0
    width_scale = measurements['chest'] / 90.0

    # Ajuster la forme selon les mesures
    mock_vertices[:, 1] *= height_scale  # Hauteur
    mock_vertices[:, 0] *= width_scale   # Largeur
    mock_vertices[:, 2] *= (measurements['waist'] / 75.0)  # Profondeur

    # Ajustements spécifiques selon le genre
    if gender == 'female':
        # Ajustements pour la morphologie féminine
        mock_vertices[:, 0] *= 0.95  # Épaules légèrement plus étroites
        # Ajuster la région des hanches (approximatif)
        hip_indices = np.where((mock_vertices[:, 1] > -0.3) & (mock_vertices[:, 1] < 0.1))[0]
        mock_vertices[hip_indices, 0] *= (measurements['hips'] / measurements['chest'])

    logger.info("Modèle simulé amélioré généré : %s vertices", vertices_count)

    return JsonResponse({
        "success": True,
        "message": "Modèle 3D simulé généré avec mesures personnalisées",
        "model_type": "SIMULATION_ENHANCED",
        "vertices_count": vertices_count,
        "faces_count": faces_count,
        "gender": gender,
        "measurements": measurements,
        "betas": betas.tolist(),
        "model_info": {
            "smpl_version": "Simulation",
            "num_betas": len(betas),
            "has_real_model": False,
            "note": "Modèle simulé basé sur vos mesures. Pour un modèle SMPL réel, installez les modèles depuis https://smpl.is.tue.mpg.de/"
        },
        "measurement_analysis": {
            "height_category": "tall" if measurements['height'] > 175 else "average" if measurements['height'] > 165 else "short",
            "body_type": analyze_body_type(measurements),
            "bmi": calculate_bmi(measurements['height'], measurements['weight'])
        }
    })

def analyze_body_type(measurements):
    """Analyse le type de morphologie basé sur les mesures"""
    waist_hip_ratio = measurements['waist'] / measurements['hips']
    chest_waist_ratio = measurements['chest'] / measurements['waist']

    if waist_hip_ratio < 0.8 and chest_waist_ratio > 1.2:
        return "sablier"  # Hourglass
    elif measurements['chest'] > measurements['hips'] * 1.05:
        return "triangle_inverse"  # Inverted triangle
    elif measurements['hips'] > measurements['chest'] * 1.05:
        return "poire"  # Pear
    elif abs(measurements['chest'] - measurements['waist']) < 10 and abs(measurements['waist'] - measurements['hips']) < 10:
        return "rectangle"  # Rectangle
    else:
        return "ovale"  # Oval

def calculate_bmi(height_cm, weight_kg):
    """Calcule l'IMC"""
    height_m = height_cm / 100.0
    bmi = weight_kg / (height_m ** 2)
    return round(bmi, 1)

@csrf_exempt
def generate_mannequin_view_mock(request):
    """Version de test qui simule la génération d'un mannequin sans les vrais modèles SMPL (DEPRECATED)"""
    if request.method == 'POST':
        try:
            logger.debug("Requête reçue : %s", request.body)
            data = json.loads(request.body)
            height = float(data.get("taille", 170))
            chest = float(data.get("poitrine", 90))
            waist = float(data.get("tour_taille", 70))
            hips = float(data.get("hanches", 90))
            gender = data.get("genre", "male").lower()

            logger.debug("Paramètres reçus : taille=%s, poitrine=%s, tour_taille=%s, hanches=%s, genre=%s",
                         height, chest, waist, hips, gender)

            # Simulation des données d'un mannequin SMPL
            # Un modèle SMPL standard a 6890 vertices et 13776 faces
            vertices_count = 6890
            faces_count = 13776

            # Génération de betas fictifs basés sur les mesures
            betas = np.zeros(10)
            betas[0] = (height - 170) / 20.0    # Taille générale
            betas[1] = (chest - 90) / 15.0      # Poitrine/largeur
            betas[2] = (waist - 70) / 12.0      # Tour de taille
            betas[3] = (hips - 90) / 15.0       # Hanches

            logger.debug("Betas calculés (simulation) : %s", betas.tolist())

            # Génération de vertices fictifs (pour la démo)
            # Dans un vrai cas, ces données viendraient du modèle SMPL
            mock_vertices = np.random.randn(vertices_count, 3) * 0.1

            # Ajustement basique basé sur la taille
            height_scale = height / 170.0
            mock_vertices[:, 1] *= height_scale  # Ajuste la hauteur (axe Y)

            logger.debug("Mannequin simulé généré avec succès : %s sommets", vertices_count)

            return JsonResponse({
                "message": "Mannequin 3D simulé généré avec succès (VERSION TEST)",
                "vertices_count": vertices_count,
                "faces_count": faces_count,
                "gender": gender,
                "measurements": {
                    "height": height,
                    "chest": chest,
                    "waist": waist,
                    "hips": hips
                },
                "betas": betas.tolist(),
                "note": "Ceci est une simulation. Pour utiliser de vrais modèles SMPL, téléchargez-les depuis https://smpl.is.tue.mpg.de/"
            })

        except Exception as e:
            logger.error("Erreur lors de la génération du mannequin simulé : %s", str(e), exc_info=True)
            return JsonResponse({"error": f"Erreur interne : {str(e)}"}, status=500)

    return JsonResponse({"error": "Méthode non autorisée"}, status=405)