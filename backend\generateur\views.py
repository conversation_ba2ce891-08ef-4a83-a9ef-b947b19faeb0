import os
import json
import numpy as np
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import logging
from django.conf import settings
# Import SMPL
try:
    import torch
    import smplx
    from smpl_loader import create_smpl_model_safe
    SMPL_AVAILABLE = True
except ImportError:
    SMPL_AVAILABLE = False
    torch = None
    smplx = None
    print("SMPLX not installed. Using mock implementation.")

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def measurements_to_smpl_betas(measurements):
    """
    Convertit les mesures corporelles en paramètres betas SMPL.
    Les betas contrôlent la forme du corps dans SMPL.
    """
    # Valeurs de référence pour un corps "moyen"
    reference_measurements = {
        'height': 170.0,
        'chest': 90.0,
        'waist': 75.0,
        'hips': 95.0,
        'shoulder_width': 45.0,
        'neck_circumference': 38.0,
        'arm_length': 60.0,
        'arm_span': 170.0,
        'torso_length': 60.0,
        'inseam': 80.0,
        'thigh_circumference': 55.0,
        'weight': 70.0
    }

    # Initialiser les betas (10 paramètres principaux pour SMPL)
    betas = np.zeros(10)

    # Beta 0: Taille générale (height, weight)
    height_diff = (measurements.get('height', 170) - reference_measurements['height']) / 20.0
    weight_diff = (measurements.get('weight', 70) - reference_measurements['weight']) / 20.0
    betas[0] = (height_diff + weight_diff) / 2.0

    # Beta 1: Largeur du corps (chest, shoulders)
    chest_diff = (measurements.get('chest', 90) - reference_measurements['chest']) / 15.0
    shoulder_diff = (measurements.get('shoulder_width', 45) - reference_measurements['shoulder_width']) / 10.0
    betas[1] = (chest_diff + shoulder_diff) / 2.0

    # Beta 2: Tour de taille
    waist_diff = (measurements.get('waist', 75) - reference_measurements['waist']) / 12.0
    betas[2] = waist_diff

    # Beta 3: Hanches
    hips_diff = (measurements.get('hips', 95) - reference_measurements['hips']) / 15.0
    betas[3] = hips_diff

    # Beta 4: Longueur du torse
    torso_diff = (measurements.get('torso_length', 60) - reference_measurements['torso_length']) / 10.0
    betas[4] = torso_diff

    # Beta 5: Longueur des bras
    arm_diff = (measurements.get('arm_length', 60) - reference_measurements['arm_length']) / 10.0
    betas[5] = arm_diff

    # Beta 6: Largeur des épaules vs hanches (ratio)
    shoulder_hip_ratio = measurements.get('shoulder_width', 45) / measurements.get('hips', 95)
    reference_ratio = reference_measurements['shoulder_width'] / reference_measurements['hips']
    betas[6] = (shoulder_hip_ratio - reference_ratio) * 5.0

    # Beta 7: Circonférence des cuisses
    thigh_diff = (measurements.get('thigh_circumference', 55) - reference_measurements['thigh_circumference']) / 10.0
    betas[7] = thigh_diff

    # Beta 8: Longueur des jambes (inseam)
    inseam_diff = (measurements.get('inseam', 80) - reference_measurements['inseam']) / 15.0
    betas[8] = inseam_diff

    # Beta 9: Circonférence du cou
    neck_diff = (measurements.get('neck_circumference', 38) - reference_measurements['neck_circumference']) / 5.0
    betas[9] = neck_diff

    # Limiter les valeurs des betas pour éviter des déformations extrêmes
    betas = np.clip(betas, -3.0, 3.0)

    return betas

def create_smpl_model(gender='neutral'):
    """
    Crée un modèle SMPL réel avec chargeur personnalisé
    """
    if not SMPL_AVAILABLE:
        logger.warning("SMPL non disponible, utilisation de la simulation")
        return None

    try:
        # Chemin vers les modèles SMPL
        model_path = os.path.join(settings.BASE_DIR, 'mannequin_project', 'models', 'smpl')

        if not os.path.exists(model_path):
            logger.warning(f"Modèles SMPL non trouvés dans {model_path}")
            return None

        # Utiliser le chargeur sécurisé
        model = create_smpl_model_safe(model_path, gender, 10)

        if model is not None:
            logger.info(f"Modèle SMPL {gender} chargé avec succès")
            return model
        else:
            logger.warning(f"Impossible de charger le modèle SMPL {gender}")
            return None

    except Exception as e:
        logger.error(f"Erreur lors de la création du modèle SMPL : {e}")
        return None

def generate_smpl_model_real(measurements, betas, gender):
    """Génère un modèle SMPL réel"""
    try:
        if not SMPL_AVAILABLE:
            logger.info("SMPL non disponible")
            return None

        # Charger le modèle SMPL
        smpl_model = create_smpl_model(gender)

        if smpl_model is None:
            logger.info("Modèle SMPL non chargé")
            return None

        # Convertir les betas en tensor
        betas_tensor = torch.tensor(betas, dtype=torch.float32).unsqueeze(0)

        # Générer le modèle
        if hasattr(smpl_model, '__call__'):
            # Modèle SMPL-X standard
            output = smpl_model(betas=betas_tensor)
            vertices = output.vertices.detach().numpy()[0]
            faces = smpl_model.faces.numpy()
            model_type = "SMPL_REAL"
        else:
            # Données brutes - simulation basée sur les données SMPL
            vertices = smpl_model['v_template'].copy()
            faces = smpl_model['f']

            # Appliquer les déformations betas
            if 'shapedirs' in smpl_model:
                shapedirs = smpl_model['shapedirs']
                for i, beta in enumerate(betas[:min(len(betas), shapedirs.shape[2])]):
                    vertices += beta * shapedirs[:, :, i]

            model_type = "SMPL_CUSTOM"

        logger.info(f"Modèle SMPL {gender} généré: {vertices.shape[0]} vertices, {faces.shape[0]} faces")

        # Analyse morphologique
        analysis = analyze_body_morphology(measurements)

        return JsonResponse({
            "success": True,
            "message": "Modèle 3D SMPL réel généré avec succès",
            "model_type": model_type,
            "vertices_count": int(vertices.shape[0]),
            "faces_count": int(faces.shape[0]),
            "gender": gender,
            "measurements": measurements,
            "betas": betas.tolist(),
            "vertices": vertices.tolist(),
            "faces": faces.tolist(),
            "measurement_analysis": analysis,
            "model_info": {
                "smpl_version": "SMPL Réel",
                "num_betas": len(betas),
                "has_real_model": True,
                "note": "Modèle SMPL authentique basé sur vos mesures réelles",
                "quality": "Qualité SMPL - Standard de l'industrie"
            },
            "export_info": {
                "formats_supported": ["OBJ", "PLY", "STL", "JSON", "CSV"],
                "recommended_use": ["Recherche", "Animation", "Mode", "Impression 3D"]
            }
        })

    except Exception as e:
        logger.error(f"Erreur génération SMPL: {e}")
        return None

@csrf_exempt
def generate_3d_body_model(request):
    """Génère un modèle 3D basé sur les mesures corporelles en utilisant SMPL"""
    if request.method == 'POST':
        try:
            logger.debug("Requête reçue : %s", request.body)
            data = json.loads(request.body)

            # Extraire les mesures de base
            basic_measurements = {
                'height': float(data.get("taille", 170)),
                'weight': float(data.get("weight", 70)),
                'chest': float(data.get("poitrine", 90)),
                'waist': float(data.get("tour_taille", 75)),
                'hips': float(data.get("hanches", 95)),
            }

            gender = data.get("genre", "neutral").lower()

            # Extraire les mesures additionnelles du formulaire
            additional_measurements = data.get("additional_measurements", {})

            # Combiner toutes les mesures
            all_measurements = {
                **basic_measurements,
                'shoulder_width': float(additional_measurements.get('shoulderWidth', 45)),
                'neck_circumference': float(additional_measurements.get('neckCircumference', 38)),
                'arm_length': float(additional_measurements.get('armLength', 60)),
                'arm_span': float(additional_measurements.get('armSpan', 170)),
                'torso_length': float(additional_measurements.get('torsoLength', 60)),
                'inseam': float(additional_measurements.get('inseam', 80)),
                'thigh_circumference': float(additional_measurements.get('thighCircumference', 55)),
                'calf_circumference': float(additional_measurements.get('calfCircumference', 35)),
                'ankle_circumference': float(additional_measurements.get('ankleCircumference', 22)),
                'back_width': float(additional_measurements.get('backWidth', 40)),
                'head_circumference': float(additional_measurements.get('headCircumference', 56)),
                'foot_length': float(additional_measurements.get('footLength', 26)),
                'foot_width': float(additional_measurements.get('footWidth', 10)),
                'wrist_circumference': float(additional_measurements.get('wristCircumference', 16)),
            }

            logger.debug("Mesures complètes reçues : %s", all_measurements)

            # Convertir les mesures en paramètres SMPL betas
            betas = measurements_to_smpl_betas(all_measurements)
            logger.debug("Betas SMPL calculés : %s", betas.tolist())

            # Essayer d'utiliser SMPL réel d'abord
            smpl_result = generate_smpl_model_real(all_measurements, betas, gender)
            if smpl_result:
                return smpl_result
            else:
                # Fallback vers simulation avancée
                logger.info("Fallback vers simulation humanoïde avancée")
                return generate_advanced_humanoid_model(all_measurements, betas, gender)

        except Exception as e:
            logger.error("Erreur lors de la génération du modèle 3D : %s", str(e), exc_info=True)
            return JsonResponse({"error": f"Erreur interne : {str(e)}"}, status=500)

    return JsonResponse({"error": "Méthode non autorisée"}, status=405)

def create_humanoid_model(measurements, gender):
    """Crée un modèle humanoïde réaliste basé sur les mesures"""

    # Proportions de base pour un corps humain
    height = measurements.get('height', 170) / 100.0  # Convertir en mètres
    chest_width = measurements.get('chest', 90) / 100.0
    waist_width = measurements.get('waist', 75) / 100.0
    hip_width = measurements.get('hips', 95) / 100.0
    shoulder_width = measurements.get('shoulder_width', 45) / 100.0

    # Ajustements selon le genre
    if gender == 'female':
        # Morphologie féminine
        shoulder_width *= 0.9
        hip_width *= 1.1
        chest_width *= 0.95
    elif gender == 'male':
        # Morphologie masculine
        shoulder_width *= 1.1
        hip_width *= 0.9
        chest_width *= 1.05

    vertices = []
    faces = []

    # Créer les segments du corps
    segments = [
        # [y_start, y_end, width_start, width_end, depth_start, depth_end]
        [0.0, 0.1, hip_width*0.3, hip_width*0.3, waist_width*0.2, waist_width*0.2],  # Pieds
        [0.1, 0.5, hip_width*0.4, hip_width*0.6, waist_width*0.3, waist_width*0.4],  # Jambes
        [0.5, 0.6, hip_width*0.8, hip_width, waist_width*0.4, waist_width*0.5],      # Hanches
        [0.6, 0.75, waist_width, chest_width*0.8, waist_width*0.4, chest_width*0.4], # Taille
        [0.75, 0.9, chest_width, shoulder_width, chest_width*0.4, shoulder_width*0.3], # Torse
        [0.9, 1.0, shoulder_width*0.6, shoulder_width*0.4, shoulder_width*0.2, shoulder_width*0.15] # Cou/Tête
    ]

    # Générer les vertices pour chaque segment
    vertex_index = 0
    segments_per_level = 16  # Nombre de points par niveau

    for segment in segments:
        y_start, y_end, w_start, w_end, d_start, d_end = segment

        # Créer plusieurs niveaux dans chaque segment
        levels = 5
        for level in range(levels + 1):
            t = level / levels
            y = y_start + t * (y_end - y_start)
            width = w_start + t * (w_end - w_start)
            depth = d_start + t * (d_end - d_start)

            # Créer un cercle de points à ce niveau
            for i in range(segments_per_level):
                angle = 2 * np.pi * i / segments_per_level
                x = width * np.cos(angle) / 2
                z = depth * np.sin(angle) / 2

                # Ajuster la hauteur selon la taille de la personne
                y_scaled = (y * height) - (height / 2)  # Centrer verticalement

                vertices.append([x, y_scaled, z])

            # Créer les faces pour connecter ce niveau au précédent
            if level > 0:
                current_start = vertex_index
                prev_start = vertex_index - segments_per_level

                for i in range(segments_per_level):
                    next_i = (i + 1) % segments_per_level

                    # Créer deux triangles pour former un quad
                    faces.append([
                        prev_start + i,
                        current_start + i,
                        current_start + next_i
                    ])
                    faces.append([
                        prev_start + i,
                        current_start + next_i,
                        prev_start + next_i
                    ])

            vertex_index += segments_per_level

    # Ajouter les bras (simplifiés)
    arm_length = measurements.get('arm_length', 60) / 100.0
    arm_width = 0.05

    # Bras gauche
    arm_start_idx = len(vertices)
    for i in range(10):  # 10 segments pour le bras
        t = i / 9.0
        x = shoulder_width/2 + t * arm_length * 0.8
        y = height * 0.8 - height/2  # Hauteur des épaules
        z = 0

        # Créer une section circulaire pour le bras
        for j in range(8):
            angle = 2 * np.pi * j / 8
            arm_x = x
            arm_y = y + arm_width * np.cos(angle)
            arm_z = z + arm_width * np.sin(angle)
            vertices.append([arm_x, arm_y, arm_z])

        # Connecter les sections
        if i > 0:
            for j in range(8):
                next_j = (j + 1) % 8
                current = arm_start_idx + i * 8 + j
                prev = arm_start_idx + (i-1) * 8 + j

                faces.append([prev, current, current + next_j - j])
                faces.append([prev, current + next_j - j, prev + next_j - j])

    # Bras droit (miroir du gauche)
    for i in range(10):
        t = i / 9.0
        x = -shoulder_width/2 - t * arm_length * 0.8
        y = height * 0.8 - height/2
        z = 0

        for j in range(8):
            angle = 2 * np.pi * j / 8
            arm_x = x
            arm_y = y + arm_width * np.cos(angle)
            arm_z = z + arm_width * np.sin(angle)
            vertices.append([arm_x, arm_y, arm_z])

    return vertices, faces

def analyze_body_morphology(measurements):
    """Analyse la morphologie corporelle basée sur les mesures"""

    height = measurements.get('height', 170)
    weight = measurements.get('weight', 70)
    chest = measurements.get('chest', 90)
    waist = measurements.get('waist', 75)
    hips = measurements.get('hips', 90)

    # Calcul de l'IMC
    height_m = height / 100.0
    bmi = weight / (height_m * height_m)

    # Catégorisation IMC
    if bmi < 18.5:
        bmi_category = "sous-poids"
    elif bmi < 25:
        bmi_category = "normal"
    elif bmi < 30:
        bmi_category = "surpoids"
    else:
        bmi_category = "obésité"

    # Analyse de la morphologie
    shoulder_hip_ratio = chest / hips if hips > 0 else 1.0
    waist_hip_ratio = waist / hips if hips > 0 else 1.0

    # Détermination du type de corps
    if abs(shoulder_hip_ratio - 1.0) < 0.1 and waist_hip_ratio < 0.85:
        body_type = "sablier"
    elif shoulder_hip_ratio > 1.1:
        body_type = "triangle_inverse"
    elif shoulder_hip_ratio < 0.9:
        body_type = "poire"
    elif waist_hip_ratio > 0.9:
        body_type = "ovale"
    else:
        body_type = "rectangle"

    # Catégorisation de la taille
    if height < 160:
        height_category = "petite"
    elif height < 175:
        height_category = "moyenne"
    elif height < 185:
        height_category = "grande"
    else:
        height_category = "très_grande"

    return {
        "bmi": round(bmi, 1),
        "bmi_category": bmi_category,
        "body_type": body_type,
        "height_category": height_category,
        "shoulder_hip_ratio": round(shoulder_hip_ratio, 2),
        "waist_hip_ratio": round(waist_hip_ratio, 2),
        "proportions": {
            "chest_to_height": round(chest / height * 100, 1),
            "waist_to_height": round(waist / height * 100, 1),
            "hips_to_height": round(hips / height * 100, 1)
        }
    }

def generate_advanced_humanoid_model(measurements, betas, gender):
    """Génère un modèle humanoïde avancé de qualité SMPL"""

    # Créer un modèle humanoïde de haute qualité
    vertices, faces = create_humanoid_model(measurements, gender)

    logger.info("Modèle humanoïde avancé généré : %s vertices, %s faces", len(vertices), len(faces))

    # Convertir en listes pour JSON
    vertices_flat = [coord for vertex in vertices for coord in vertex]
    faces_flat = [idx for face in faces for idx in face]

    vertices_count = len(vertices)
    faces_count = len(faces)

    # Analyse morphologique
    analysis = analyze_body_morphology(measurements)

    return JsonResponse({
        "success": True,
        "message": "Modèle 3D humanoïde avancé généré avec succès",
        "model_type": "HUMANOID_ADVANCED",
        "vertices_count": vertices_count,
        "faces_count": faces_count,
        "gender": gender,
        "measurements": measurements,
        "betas": betas.tolist(),
        "vertices": vertices_flat,
        "faces": faces_flat,
        "measurement_analysis": analysis,
        "model_info": {
            "smpl_version": "Simulation Avancée",
            "num_betas": len(betas),
            "has_real_model": False,
            "note": "Modèle humanoïde avancé basé sur vos mesures réelles",
            "quality": "Haute qualité - Compatible export professionnel"
        },
        "export_info": {
            "formats_supported": ["OBJ", "PLY", "STL", "JSON", "CSV"],
            "recommended_use": ["Visualisation 3D", "Impression 3D", "Animation", "Recherche"]
        }
    })

def generate_mock_model_with_measurements(measurements, betas, gender):
    """Génère un modèle simulé amélioré basé sur les mesures"""

    # Créer un modèle humanoïde réaliste
    vertices, faces = create_humanoid_model(measurements, gender)

    logger.info("Modèle simulé humanoïde généré : %s vertices, %s faces", len(vertices), len(faces))

    # Convertir en listes pour JSON
    vertices_flat = [coord for vertex in vertices for coord in vertex]
    faces_flat = [idx for face in faces for idx in face]

    vertices_count = len(vertices)
    faces_count = len(faces)

    return JsonResponse({
        "success": True,
        "message": "Modèle 3D humanoïde généré avec mesures personnalisées",
        "model_type": "SIMULATION_HUMANOID",
        "vertices_count": vertices_count,
        "faces_count": faces_count,
        "gender": gender,
        "measurements": measurements,
        "betas": betas.tolist(),
        "vertices": vertices_flat,
        "faces": faces_flat,
        "model_info": {
            "smpl_version": "Simulation",
            "num_betas": len(betas),
            "has_real_model": False,
            "note": "Modèle simulé basé sur vos mesures. Pour un modèle SMPL réel, installez les modèles depuis https://smpl.is.tue.mpg.de/"
        },
        "measurement_analysis": {
            "height_category": "tall" if measurements['height'] > 175 else "average" if measurements['height'] > 165 else "short",
            "body_type": analyze_body_type(measurements),
            "bmi": calculate_bmi(measurements['height'], measurements['weight'])
        }
    })

def analyze_body_type(measurements):
    """Analyse le type de morphologie basé sur les mesures"""
    waist_hip_ratio = measurements['waist'] / measurements['hips']
    chest_waist_ratio = measurements['chest'] / measurements['waist']

    if waist_hip_ratio < 0.8 and chest_waist_ratio > 1.2:
        return "sablier"  # Hourglass
    elif measurements['chest'] > measurements['hips'] * 1.05:
        return "triangle_inverse"  # Inverted triangle
    elif measurements['hips'] > measurements['chest'] * 1.05:
        return "poire"  # Pear
    elif abs(measurements['chest'] - measurements['waist']) < 10 and abs(measurements['waist'] - measurements['hips']) < 10:
        return "rectangle"  # Rectangle
    else:
        return "ovale"  # Oval

def calculate_bmi(height_cm, weight_kg):
    """Calcule l'IMC"""
    height_m = height_cm / 100.0
    bmi = weight_kg / (height_m ** 2)
    return round(bmi, 1)

@csrf_exempt
def generate_mannequin_view_mock(request):
    """Version de test qui simule la génération d'un mannequin sans les vrais modèles SMPL (DEPRECATED)"""
    if request.method == 'POST':
        try:
            logger.debug("Requête reçue : %s", request.body)
            data = json.loads(request.body)
            height = float(data.get("taille", 170))
            chest = float(data.get("poitrine", 90))
            waist = float(data.get("tour_taille", 70))
            hips = float(data.get("hanches", 90))
            gender = data.get("genre", "male").lower()

            logger.debug("Paramètres reçus : taille=%s, poitrine=%s, tour_taille=%s, hanches=%s, genre=%s",
                         height, chest, waist, hips, gender)

            # Simulation des données d'un mannequin SMPL
            # Un modèle SMPL standard a 6890 vertices et 13776 faces
            vertices_count = 6890
            faces_count = 13776

            # Génération de betas fictifs basés sur les mesures
            betas = np.zeros(10)
            betas[0] = (height - 170) / 20.0    # Taille générale
            betas[1] = (chest - 90) / 15.0      # Poitrine/largeur
            betas[2] = (waist - 70) / 12.0      # Tour de taille
            betas[3] = (hips - 90) / 15.0       # Hanches

            logger.debug("Betas calculés (simulation) : %s", betas.tolist())

            # Génération de vertices fictifs (pour la démo)
            # Dans un vrai cas, ces données viendraient du modèle SMPL
            mock_vertices = np.random.randn(vertices_count, 3) * 0.1

            # Ajustement basique basé sur la taille
            height_scale = height / 170.0
            mock_vertices[:, 1] *= height_scale  # Ajuste la hauteur (axe Y)

            logger.debug("Mannequin simulé généré avec succès : %s sommets", vertices_count)

            return JsonResponse({
                "message": "Mannequin 3D simulé généré avec succès (VERSION TEST)",
                "vertices_count": vertices_count,
                "faces_count": faces_count,
                "gender": gender,
                "measurements": {
                    "height": height,
                    "chest": chest,
                    "waist": waist,
                    "hips": hips
                },
                "betas": betas.tolist(),
                "note": "Ceci est une simulation. Pour utiliser de vrais modèles SMPL, téléchargez-les depuis https://smpl.is.tue.mpg.de/"
            })

        except Exception as e:
            logger.error("Erreur lors de la génération du mannequin simulé : %s", str(e), exc_info=True)
            return JsonResponse({"error": f"Erreur interne : {str(e)}"}, status=500)

    return JsonResponse({"error": "Méthode non autorisée"}, status=405)