#!/usr/bin/env python3
"""
Test de validation des données avec champs vides
"""

import sys
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mannequin_project.settings')
django.setup()

def test_empty_fields():
    """Test avec des champs vides"""
    print("🧪 Test Validation Champs Vides")
    print("=" * 50)
    
    try:
        from generateur.views import validate_and_clean_measurements, safe_float
        
        # Test de safe_float
        print("🔧 Test safe_float:")
        test_cases = [
            ("", 10.0, "chaîne vide"),
            (None, 10.0, "None"),
            ("undefined", 10.0, "undefined"),
            ("abc", 10.0, "texte invalide"),
            ("123.45", 10.0, "nombre valide"),
            (123.45, 10.0, "float valide"),
            (0, 10.0, "zéro")
        ]
        
        for value, default, description in test_cases:
            result = safe_float(value, default)
            expected = 123.45 if value == "123.45" else (123.45 if value == 123.45 else (0.0 if value == 0 else default))
            print(f"   - {description}: '{value}' -> {result} (attendu: {expected})")
            assert result == expected, f"Erreur pour {description}"
        
        print("✅ safe_float fonctionne correctement")
        
        # Test avec données partiellement vides
        print("\n📋 Test données partiellement vides:")
        test_data = {
            "taille": "175",
            "weight": "",  # Vide
            "poitrine": "95",
            "tour_taille": "",  # Vide
            "hanches": "90",
            "genre": "male",
            "additional_measurements": {
                "shoulderWidth": "45",
                "neckCircumference": "",  # Vide
                "armLength": "65",
                "armSpan": "",  # Vide
                "torsoLength": "60",
                "inseam": "",  # Vide
                "thighCircumference": "55",
                "calfCircumference": "",  # Vide
                "ankleCircumference": "22",
                "backWidth": "",  # Vide
                "headCircumference": "56",
                "footLength": "",  # Vide
                "footWidth": "10",
                "wristCircumference": ""  # Vide
            }
        }
        
        print(f"Données d'entrée: {test_data}")
        
        # Valider et nettoyer
        cleaned = validate_and_clean_measurements(test_data)
        
        print(f"Données nettoyées: {cleaned}")
        
        # Vérifications
        assert cleaned['height'] == 175.0, "Taille incorrecte"
        assert cleaned['weight'] == 70.0, "Poids par défaut incorrect"  # Valeur par défaut
        assert cleaned['chest'] == 95.0, "Poitrine incorrecte"
        assert cleaned['waist'] == 75.0, "Taille par défaut incorrecte"  # Valeur par défaut
        assert cleaned['hips'] == 90.0, "Hanches incorrectes"
        
        print("✅ Validation des données fonctionne correctement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_extreme_values():
    """Test avec des valeurs extrêmes"""
    print("\n🔬 Test Valeurs Extrêmes")
    print("=" * 50)
    
    try:
        from generateur.views import validate_and_clean_measurements
        
        # Données avec valeurs extrêmes
        extreme_data = {
            "taille": "500",  # Trop grand
            "weight": "10",   # Trop petit
            "poitrine": "300", # Trop grand
            "tour_taille": "5", # Trop petit
            "hanches": "400",  # Trop grand
            "genre": "male"
        }
        
        cleaned = validate_and_clean_measurements(extreme_data)
        
        print(f"Données extrêmes: {extreme_data}")
        print(f"Données limitées: {cleaned}")
        
        # Vérifier que les limites sont appliquées
        assert 100 <= cleaned['height'] <= 250, f"Taille non limitée: {cleaned['height']}"
        assert 30 <= cleaned['weight'] <= 300, f"Poids non limité: {cleaned['weight']}"
        assert 50 <= cleaned['chest'] <= 200, f"Poitrine non limitée: {cleaned['chest']}"
        assert 40 <= cleaned['waist'] <= 180, f"Taille non limitée: {cleaned['waist']}"
        assert 50 <= cleaned['hips'] <= 200, f"Hanches non limitées: {cleaned['hips']}"
        
        print("✅ Limitation des valeurs extrêmes fonctionne")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_full_pipeline():
    """Test du pipeline complet avec données problématiques"""
    print("\n🔄 Test Pipeline Complet")
    print("=" * 50)
    
    try:
        from generateur.views import validate_and_clean_measurements, measurements_to_smpl_betas
        
        # Données avec tous types de problèmes
        problematic_data = {
            "taille": "",
            "weight": "abc",
            "poitrine": None,
            "tour_taille": "undefined",
            "hanches": "85",
            "genre": "invalid_gender",
            "additional_measurements": {
                "shoulderWidth": "",
                "neckCircumference": "xyz",
                "armLength": None,
                "armSpan": "170",
                "torsoLength": "",
                "inseam": "82",
                "thighCircumference": "",
                "calfCircumference": "35",
                "ankleCircumference": "",
                "backWidth": "40",
                "headCircumference": "",
                "footLength": "26",
                "footWidth": "",
                "wristCircumference": "16"
            }
        }
        
        print("🔧 Validation des données...")
        cleaned = validate_and_clean_measurements(problematic_data)
        print(f"Données nettoyées: {cleaned}")
        
        print("🧬 Conversion en betas SMPL...")
        betas = measurements_to_smpl_betas(cleaned)
        print(f"Betas calculés: {betas}")
        
        # Vérifications
        assert len(betas) == 10, "Nombre de betas incorrect"
        assert all(isinstance(b, (int, float)) for b in betas), "Betas non numériques"
        assert all(-5.0 <= b <= 5.0 for b in betas), "Betas hors limites"
        
        print("✅ Pipeline complet fonctionne avec données problématiques")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🚀 Tests de Validation Complète")
    print("=" * 60)
    
    success_count = 0
    
    if test_empty_fields():
        success_count += 1
    
    if test_extreme_values():
        success_count += 1
    
    if test_full_pipeline():
        success_count += 1
    
    print(f"\n📊 Résultats: {success_count}/3 tests réussis")
    
    if success_count == 3:
        print("\n🎉 Validation complètement fonctionnelle!")
        print("💡 L'erreur 'could not convert string to float' est corrigée")
        print("🔗 Testez maintenant sur: http://localhost:5174/3d-body")
    else:
        print("\n⚠️ Certains tests ont échoué")
        print("💡 Vérifiez les logs pour plus de détails")

if __name__ == "__main__":
    main()
