#!/usr/bin/env python3
"""
Debug de la forme SMPL générée
"""

import sys
import numpy as np

# Ajouter le répertoire courant au path
sys.path.append('.')

def debug_smpl_data():
    """Debug des données SMPL"""
    print("🔍 Debug Données SMPL")
    print("=" * 40)
    
    try:
        from smpl_loader import create_smpl_model_safe
        
        model_path = "mannequin_project/models/smpl"
        gender = 'male'
        
        print(f"🔧 Chargement modèle {gender}...")
        smpl_model = create_smpl_model_safe(model_path, gender, 10)
        
        if smpl_model is None:
            print("❌ Modèle non chargé")
            return False
        
        print("✅ Modèle chargé")
        
        # Analyser les données
        if hasattr(smpl_model, '__call__'):
            print("📊 Type: Modèle SMPL-X")
            # Test avec betas zéro (forme de base)
            import torch
            betas = torch.zeros(1, 10)
            output = smpl_model(betas=betas)
            vertices = output.vertices.detach().numpy()[0]
            faces = smpl_model.faces.numpy()
        else:
            print("📊 Type: Données SMPL brutes")
            vertices = smpl_model['v_template']
            faces = smpl_model['f']
        
        print(f"📐 Analyse des vertices:")
        print(f"   - Shape: {vertices.shape}")
        print(f"   - Min: [{vertices.min(axis=0)}]")
        print(f"   - Max: [{vertices.max(axis=0)}]")
        print(f"   - Mean: [{vertices.mean(axis=0)}]")
        print(f"   - Std: [{vertices.std(axis=0)}]")
        
        # Vérifier si les vertices sont dans une plage normale
        x_range = vertices[:, 0].max() - vertices[:, 0].min()
        y_range = vertices[:, 1].max() - vertices[:, 1].min()
        z_range = vertices[:, 2].max() - vertices[:, 2].min()
        
        print(f"📏 Dimensions:")
        print(f"   - Largeur (X): {x_range:.3f}")
        print(f"   - Hauteur (Y): {y_range:.3f}")
        print(f"   - Profondeur (Z): {z_range:.3f}")
        
        # Vérifier les proportions
        if y_range > x_range and y_range > z_range:
            print("✅ Proportions humanoïdes détectées")
        else:
            print("⚠️ Proportions anormales détectées")
        
        # Analyser les faces
        print(f"🔺 Analyse des faces:")
        print(f"   - Shape: {faces.shape}")
        print(f"   - Min index: {faces.min()}")
        print(f"   - Max index: {faces.max()}")
        print(f"   - Vertices count: {vertices.shape[0]}")
        
        if faces.max() >= vertices.shape[0]:
            print("❌ Indices de faces invalides!")
            return False
        else:
            print("✅ Indices de faces valides")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_proper_humanoid():
    """Créer un modèle humanoïde correct"""
    print("\n🧑 Création Modèle Humanoïde Correct")
    print("=" * 50)
    
    # Créer un modèle humanoïde simple mais correct
    vertices = []
    faces = []
    
    # Paramètres du corps
    height = 1.75  # 175cm
    width = 0.4    # 40cm largeur
    depth = 0.2    # 20cm profondeur
    
    # Créer les segments du corps de bas en haut
    segments = [
        # [y_start, y_end, width_factor, depth_factor, name]
        [0.0, 0.1, 0.15, 0.1, "pieds"],
        [0.1, 0.5, 0.2, 0.15, "jambes"],
        [0.5, 0.65, 0.35, 0.25, "hanches"],
        [0.65, 0.8, 0.3, 0.2, "taille"],
        [0.8, 0.95, 0.4, 0.25, "torse"],
        [0.95, 1.0, 0.2, 0.15, "cou/tête"]
    ]
    
    vertex_index = 0
    points_per_level = 12  # Nombre de points par niveau
    
    for segment in segments:
        y_start, y_end, w_factor, d_factor, name = segment
        
        # Créer plusieurs niveaux dans chaque segment
        levels = 4
        for level in range(levels + 1):
            t = level / levels
            y = y_start + t * (y_end - y_start)
            
            # Ajuster la hauteur
            y_scaled = (y * height) - (height / 2)
            
            # Créer un cercle de points à ce niveau
            for i in range(points_per_level):
                angle = 2 * np.pi * i / points_per_level
                x = width * w_factor * np.cos(angle) / 2
                z = depth * d_factor * np.sin(angle) / 2
                
                vertices.append([x, y_scaled, z])
            
            # Créer les faces pour connecter ce niveau au précédent
            if level > 0:
                current_start = vertex_index
                prev_start = vertex_index - points_per_level
                
                for i in range(points_per_level):
                    next_i = (i + 1) % points_per_level
                    
                    # Créer deux triangles pour former un quad
                    faces.append([
                        prev_start + i,
                        current_start + i,
                        current_start + next_i
                    ])
                    faces.append([
                        prev_start + i,
                        current_start + next_i,
                        prev_start + next_i
                    ])
            
            vertex_index += points_per_level
    
    # Ajouter des bras simplifiés
    arm_length = 0.6
    arm_radius = 0.05
    
    # Bras gauche
    arm_y = height * 0.8 - height/2  # Hauteur des épaules
    arm_segments = 8
    arm_points = 6
    
    for side in [-1, 1]:  # Gauche et droite
        arm_start_idx = len(vertices)
        
        for seg in range(arm_segments + 1):
            t = seg / arm_segments
            x = side * (width * 0.2 + t * arm_length)
            y = arm_y - t * 0.1  # Légère pente vers le bas
            
            for p in range(arm_points):
                angle = 2 * np.pi * p / arm_points
                arm_y_offset = arm_radius * np.cos(angle)
                arm_z_offset = arm_radius * np.sin(angle)
                
                vertices.append([x, y + arm_y_offset, arm_z_offset])
            
            # Connecter les segments
            if seg > 0:
                current_start = arm_start_idx + seg * arm_points
                prev_start = arm_start_idx + (seg - 1) * arm_points
                
                for p in range(arm_points):
                    next_p = (p + 1) % arm_points
                    
                    faces.append([
                        prev_start + p,
                        current_start + p,
                        current_start + next_p
                    ])
                    faces.append([
                        prev_start + p,
                        current_start + next_p,
                        prev_start + next_p
                    ])
    
    vertices = np.array(vertices)
    faces = np.array(faces)
    
    print(f"✅ Modèle humanoïde créé:")
    print(f"   - Vertices: {vertices.shape}")
    print(f"   - Faces: {faces.shape}")
    print(f"   - Hauteur: {vertices[:, 1].max() - vertices[:, 1].min():.3f}m")
    print(f"   - Largeur: {vertices[:, 0].max() - vertices[:, 0].min():.3f}m")
    
    return vertices, faces

def main():
    """Fonction principale"""
    print("🚀 Debug et Correction Forme SMPL")
    print("=" * 60)
    
    # Debug des données SMPL actuelles
    smpl_ok = debug_smpl_data()
    
    if not smpl_ok:
        print("\n⚠️ Problème avec les données SMPL détecté")
    
    # Créer un modèle humanoïde correct
    vertices, faces = create_proper_humanoid()
    
    print(f"\n💡 Solution recommandée:")
    print(f"   - Remplacer les données SMPL problématiques")
    print(f"   - Utiliser le modèle humanoïde correct")
    print(f"   - {vertices.shape[0]:,} vertices, {faces.shape[0]:,} faces")
    
    # Sauvegarder les données correctes
    np.save('correct_humanoid_vertices.npy', vertices)
    np.save('correct_humanoid_faces.npy', faces)
    
    print(f"\n✅ Données correctes sauvegardées:")
    print(f"   - correct_humanoid_vertices.npy")
    print(f"   - correct_humanoid_faces.npy")

if __name__ == "__main__":
    main()
