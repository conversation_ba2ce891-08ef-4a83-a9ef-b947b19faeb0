import React, { useState, useEffect } from 'react';

const SMPLParameterTuner = ({ initialBetas = [], onBetasChange, measurements = {} }) => {
  const [betas, setBetas] = useState(initialBetas.length ? initialBetas : new Array(10).fill(0));
  const [isExpanded, setIsExpanded] = useState(false);
  const [presets, setPresets] = useState([]);

  // Descriptions des paramètres beta SMPL
  const betaDescriptions = [
    { name: 'Taille générale', description: 'Contrôle la taille globale du corps', range: [-3, 3] },
    { name: 'Largeur du corps', description: 'Contrôle la largeur des épaules et du torse', range: [-2, 2] },
    { name: 'Tour de taille', description: 'Contrôle la circonférence de la taille', range: [-2, 2] },
    { name: 'Tour de hanches', description: 'Contrôle la largeur des hanches', range: [-2, 2] },
    { name: 'Longueur du torse', description: 'Contrôle la longueur du torse', range: [-1.5, 1.5] },
    { name: 'Longueur des bras', description: 'Contrôle la longueur des bras', range: [-1.5, 1.5] },
    { name: 'Ratio épaules/hanches', description: 'Contrôle le rapport épaules/hanches', range: [-1, 1] },
    { name: 'Circonférence des cuisses', description: 'Contrôle la taille des cuisses', range: [-1.5, 1.5] },
    { name: 'Longueur des jambes', description: 'Contrôle la longueur des jambes', range: [-1.5, 1.5] },
    { name: 'Circonférence du cou', description: 'Contrôle la taille du cou', range: [-1, 1] }
  ];

  // Presets prédéfinis
  const defaultPresets = [
    {
      name: 'Corps athlétique',
      description: 'Morphologie sportive et musclée',
      betas: [0.2, 0.5, -0.3, 0.1, 0.2, 0.1, 0.3, 0.2, 0.1, 0.0]
    },
    {
      name: 'Corps mince',
      description: 'Morphologie élancée et fine',
      betas: [-0.3, -0.4, -0.2, -0.3, 0.1, 0.0, 0.0, -0.3, 0.2, -0.1]
    },
    {
      name: 'Corps robuste',
      description: 'Morphologie plus large et robuste',
      betas: [0.4, 0.6, 0.3, 0.4, 0.0, 0.0, 0.0, 0.3, 0.0, 0.1]
    },
    {
      name: 'Réinitialiser',
      description: 'Remettre tous les paramètres à zéro',
      betas: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    }
  ];

  useEffect(() => {
    setPresets(defaultPresets);
  }, []);

  useEffect(() => {
    if (initialBetas.length) {
      setBetas(initialBetas);
    }
  }, [initialBetas]);

  const handleBetaChange = (index, value) => {
    const newBetas = [...betas];
    newBetas[index] = parseFloat(value);
    setBetas(newBetas);
    
    if (onBetasChange) {
      onBetasChange(newBetas);
    }
  };

  const applyPreset = (preset) => {
    setBetas(preset.betas);
    if (onBetasChange) {
      onBetasChange(preset.betas);
    }
  };

  const generateRandomVariation = () => {
    const newBetas = betas.map((beta, index) => {
      const [min, max] = betaDescriptions[index].range;
      const variation = (Math.random() - 0.5) * 0.5; // Variation de ±0.25
      const newValue = Math.max(min, Math.min(max, beta + variation));
      return Math.round(newValue * 1000) / 1000; // Arrondir à 3 décimales
    });
    
    setBetas(newBetas);
    if (onBetasChange) {
      onBetasChange(newBetas);
    }
  };

  const saveCurrentAsPreset = () => {
    const name = prompt('Nom du preset:');
    if (name) {
      const newPreset = {
        name,
        description: 'Preset personnalisé',
        betas: [...betas]
      };
      setPresets([...presets, newPreset]);
    }
  };

  const getBetaColor = (value, range) => {
    const [min, max] = range;
    const normalized = (value - min) / (max - min);
    
    if (normalized < 0.4) return 'bg-blue-500';
    if (normalized > 0.6) return 'bg-red-500';
    return 'bg-green-500';
  };

  const getImpactLevel = (value) => {
    const abs = Math.abs(value);
    if (abs < 0.3) return 'Faible';
    if (abs < 0.7) return 'Moyen';
    if (abs < 1.2) return 'Fort';
    return 'Très fort';
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
          Affinage des Paramètres SMPL
        </h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="px-3 py-1 text-sm bg-gray-600 hover:bg-gray-700 text-white rounded-md transition duration-150"
        >
          {isExpanded ? '🔼 Réduire' : '🔽 Développer'}
        </button>
      </div>

      {isExpanded && (
        <div className="space-y-6">
          {/* Presets */}
          <div>
            <h4 className="text-md font-medium text-gray-800 dark:text-gray-100 mb-3">
              Presets Rapides
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {presets.map((preset, index) => (
                <button
                  key={index}
                  onClick={() => applyPreset(preset)}
                  className="p-2 text-sm bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition duration-150"
                  title={preset.description}
                >
                  {preset.name}
                </button>
              ))}
            </div>
            
            <div className="flex gap-2 mt-2">
              <button
                onClick={generateRandomVariation}
                className="px-3 py-1 text-sm bg-purple-600 hover:bg-purple-700 text-white rounded-md transition duration-150"
                title="Générer une variation aléatoire"
              >
                🎲 Variation aléatoire
              </button>
              <button
                onClick={saveCurrentAsPreset}
                className="px-3 py-1 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md transition duration-150"
                title="Sauvegarder comme preset"
              >
                💾 Sauvegarder
              </button>
            </div>
          </div>

          {/* Contrôles des paramètres */}
          <div>
            <h4 className="text-md font-medium text-gray-800 dark:text-gray-100 mb-3">
              Paramètres Détaillés
            </h4>
            
            <div className="space-y-4">
              {betaDescriptions.map((desc, index) => {
                const value = betas[index] || 0;
                const [min, max] = desc.range;
                
                return (
                  <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Beta {index}: {desc.name}
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {desc.description}
                        </p>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-mono text-gray-800 dark:text-gray-200">
                          {value.toFixed(3)}
                        </span>
                        <div className="text-xs text-gray-500">
                          Impact: {getImpactLevel(value)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <span className="text-xs text-gray-500">{min}</span>
                      <div className="flex-1 relative">
                        <input
                          type="range"
                          min={min}
                          max={max}
                          step="0.01"
                          value={value}
                          onChange={(e) => handleBetaChange(index, e.target.value)}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                        />
                        <div 
                          className={`absolute top-0 h-2 rounded-lg ${getBetaColor(value, desc.range)}`}
                          style={{ 
                            width: `${((value - min) / (max - min)) * 100}%`,
                            opacity: 0.7
                          }}
                        />
                      </div>
                      <span className="text-xs text-gray-500">{max}</span>
                      <input
                        type="number"
                        min={min}
                        max={max}
                        step="0.01"
                        value={value}
                        onChange={(e) => handleBetaChange(index, e.target.value)}
                        className="w-20 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-gray-100"
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Résumé des modifications */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-800 dark:text-gray-100 mb-2">
              Résumé des Modifications
            </h4>
            
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-xs">
              {betas.map((beta, index) => (
                <div key={index} className="text-center">
                  <div className="font-mono text-gray-800 dark:text-gray-200">
                    β{index}: {beta.toFixed(2)}
                  </div>
                  <div className={`text-xs ${Math.abs(beta) > 0.5 ? 'text-red-600' : 'text-gray-500'}`}>
                    {getImpactLevel(beta)}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-3 text-sm text-gray-600 dark:text-gray-400">
              <p>
                💡 <strong>Conseil:</strong> Les valeurs entre -0.5 et 0.5 donnent des résultats plus réalistes.
                Les valeurs extrêmes peuvent créer des déformations importantes.
              </p>
            </div>
          </div>

          {/* Comparaison avec les mesures originales */}
          {Object.keys(measurements).length > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 className="text-md font-medium text-blue-800 dark:text-blue-200 mb-2">
                Mesures de Référence
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                <div>
                  <span className="text-blue-700 dark:text-blue-300">Taille:</span>
                  <span className="ml-1 font-mono">{measurements.height || 'N/A'} cm</span>
                </div>
                <div>
                  <span className="text-blue-700 dark:text-blue-300">Poitrine:</span>
                  <span className="ml-1 font-mono">{measurements.chest || 'N/A'} cm</span>
                </div>
                <div>
                  <span className="text-blue-700 dark:text-blue-300">Taille:</span>
                  <span className="ml-1 font-mono">{measurements.waist || 'N/A'} cm</span>
                </div>
                <div>
                  <span className="text-blue-700 dark:text-blue-300">Hanches:</span>
                  <span className="ml-1 font-mono">{measurements.hips || 'N/A'} cm</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SMPLParameterTuner;
